version: '3.8'

services:
  postgres-test:
    image: postgres:15-alpine
    container_name: stria-postgres-test
    environment:
      POSTGRES_DB: stria_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d stria_test"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_test_data:
