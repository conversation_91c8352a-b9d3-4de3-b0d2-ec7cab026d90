import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ChangeRequestService } from '../services/change-request.service';
import {
  CreateChangeRequestDto,
  CreateChangeRequestWithAssessmentDto,
  BulkCreateChangeRequestDto,
  UpdateChangeRequestDto,
  UpdateChangeRequestStatusDto,
  AssignChangeRequestDto,
  ApproveChangeRequestDto,
  RejectChangeRequestDto,
  BulkUpdateChangeRequestDto,
  ChangeRequestResponseDto,
  ChangeRequestListResponseDto,
  ChangeRequestSummaryDto,
  ChangeRequestStatisticsDto,
} from '../dto/change-request';
import { ChangeRequestStatus, ChangeRequestPriority, ChangeRequestType } from '../enums';

/**
 * Change Request Controller
 * Sprint 7: Change Request Management System
 * 
 * RESTful API endpoints for change request operations
 * Handles CRUD operations, workflow management, and reporting
 */
@ApiTags('Change Requests')
@Controller('change-requests')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChangeRequestController {
  constructor(private readonly changeRequestService: ChangeRequestService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new change request',
    description: 'Creates a new structured change request for a project',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Change request created successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid change request data',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiBody({ type: CreateChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createChangeRequest(
    @Body() createDto: CreateChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.create(createDto, userId);
  }

  @Post('with-assessment')
  @ApiOperation({
    summary: 'Create change request with automatic impact assessment',
    description: 'Creates a change request and optionally triggers automatic impact assessment and workflow',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Change request created with assessment triggered',
    type: ChangeRequestResponseDto,
  })
  @ApiBody({ type: CreateChangeRequestWithAssessmentDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createWithAssessment(
    @Body() createDto: CreateChangeRequestWithAssessmentDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.createWithAssessment(createDto, userId);
  }

  @Post('bulk')
  @ApiOperation({
    summary: 'Bulk create change requests',
    description: 'Creates multiple change requests in a single operation',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Change requests created successfully',
    type: ChangeRequestListResponseDto,
  })
  @ApiBody({ type: BulkCreateChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkCreate(
    @Body() bulkCreateDto: BulkCreateChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestListResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.bulkCreate(bulkCreateDto, userId);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all change requests',
    description: 'Retrieves a paginated list of change requests with filtering options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change requests retrieved successfully',
    type: ChangeRequestListResponseDto,
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'projectId', required: false, type: String, description: 'Filter by project ID' })
  @ApiQuery({ name: 'status', required: false, enum: ChangeRequestStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'priority', required: false, enum: ChangeRequestPriority, description: 'Filter by priority' })
  @ApiQuery({ name: 'type', required: false, enum: ChangeRequestType, description: 'Filter by type' })
  @ApiQuery({ name: 'assignedToId', required: false, type: String, description: 'Filter by assigned user' })
  @ApiQuery({ name: 'requesterId', required: false, type: String, description: 'Filter by requester' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search in title and description' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field (default: createdAt)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order (default: DESC)' })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Filter from date (ISO string)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'Filter to date (ISO string)' })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('projectId') projectId?: string,
    @Query('status') status?: ChangeRequestStatus,
    @Query('priority') priority?: ChangeRequestPriority,
    @Query('type') type?: ChangeRequestType,
    @Query('assignedToId') assignedToId?: string,
    @Query('requesterId') requesterId?: string,
    @Query('search') search?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Request() req: any,
  ): Promise<ChangeRequestListResponseDto> {
    const userId = req.user.id;
    const options = {
      page,
      limit,
      projectId,
      status,
      priority,
      type,
      assignedToId,
      requesterId,
      search,
      sortBy,
      sortOrder,
      dateFrom,
      dateTo,
    };
    return this.changeRequestService.findAll(userId, options);
  }

  @Get('statistics')
  @ApiOperation({
    summary: 'Get change request statistics',
    description: 'Retrieves statistical data and analytics for change requests',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Statistics retrieved successfully',
    type: ChangeRequestStatisticsDto,
  })
  @ApiQuery({ name: 'projectId', required: false, type: String, description: 'Filter by project ID' })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Filter from date (ISO string)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'Filter to date (ISO string)' })
  async getStatistics(
    @Query('projectId') projectId?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Request() req: any,
  ): Promise<ChangeRequestStatisticsDto> {
    const userId = req.user.id;
    const options = { projectId, dateFrom, dateTo };
    return this.changeRequestService.getStatistics(userId, options);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get change request by ID',
    description: 'Retrieves a specific change request with full details',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change request retrieved successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Change request not found',
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.findById(id, userId);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update change request',
    description: 'Updates an existing change request with new data',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change request updated successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Change request not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Version conflict - change request was modified by another user',
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  @ApiBody({ type: UpdateChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.update(id, updateDto, userId);
  }

  @Put(':id/status')
  @ApiOperation({
    summary: 'Update change request status',
    description: 'Updates the status of a change request with optional reason',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Status updated successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid status transition',
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  @ApiBody({ type: UpdateChangeRequestStatusDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() statusDto: UpdateChangeRequestStatusDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.updateStatus(id, statusDto, userId);
  }

  @Put(':id/assign')
  @ApiOperation({
    summary: 'Assign change request',
    description: 'Assigns a change request to a specific user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change request assigned successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  @ApiBody({ type: AssignChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async assign(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignDto: AssignChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.assign(id, assignDto, userId);
  }

  @Put(':id/approve')
  @ApiOperation({
    summary: 'Approve change request',
    description: 'Approves a change request with optional conditions and budget',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change request approved successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Change request is not in pending approval status',
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  @ApiBody({ type: ApproveChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async approve(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() approveDto: ApproveChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.approve(id, approveDto, userId);
  }

  @Put(':id/reject')
  @ApiOperation({
    summary: 'Reject change request',
    description: 'Rejects a change request with mandatory reason',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change request rejected successfully',
    type: ChangeRequestResponseDto,
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  @ApiBody({ type: RejectChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async reject(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() rejectDto: RejectChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.reject(id, rejectDto, userId);
  }

  @Put('bulk-update')
  @ApiOperation({
    summary: 'Bulk update change requests',
    description: 'Updates multiple change requests with the same changes',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Change requests updated successfully',
    type: ChangeRequestListResponseDto,
  })
  @ApiBody({ type: BulkUpdateChangeRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkUpdate(
    @Body() bulkUpdateDto: BulkUpdateChangeRequestDto,
    @Request() req: any,
  ): Promise<ChangeRequestListResponseDto> {
    const userId = req.user.id;
    return this.changeRequestService.bulkUpdate(bulkUpdateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete change request',
    description: 'Deletes a change request (only allowed for draft or cancelled requests)',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Change request deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Change request not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Only draft or cancelled change requests can be deleted',
  })
  @ApiParam({ name: 'id', type: String, description: 'Change request ID' })
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<void> {
    const userId = req.user.id;
    return this.changeRequestService.delete(id, userId);
  }
}
