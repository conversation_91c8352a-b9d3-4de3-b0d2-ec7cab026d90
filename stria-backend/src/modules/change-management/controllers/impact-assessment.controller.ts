import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ImpactAssessmentService } from '../services/impact-assessment.service';
import {
  CreateImpactAssessmentDto,
  AutomatedAssessmentRequestDto,
  ManualAssessmentInputDto,
  BulkAssessmentRequestDto,
  ImpactAssessmentResponseDto,
  ImpactAssessmentListResponseDto,
  ImpactAssessmentSummaryDto,
  AssessmentComparisonDto,
  AssessmentAnalyticsDto,
} from '../dto/impact-assessment';
import { ImpactAssessmentStatus, ImpactAssessmentMethod, ImpactLevel } from '../enums';

/**
 * Impact Assessment Controller
 * Sprint 7: Change Request Management System
 * 
 * RESTful API endpoints for intelligent impact assessment operations
 * Handles automated and manual assessments, analytics, and reporting
 */
@ApiTags('Impact Assessments')
@Controller('impact-assessments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ImpactAssessmentController {
  constructor(private readonly impactAssessmentService: ImpactAssessmentService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new impact assessment',
    description: 'Creates a new impact assessment for a change request',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Impact assessment created successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid assessment data',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Impact assessment already exists for this change request',
  })
  @ApiBody({ type: CreateImpactAssessmentDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createAssessment(
    @Body() createDto: CreateImpactAssessmentDto,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.create(createDto, userId);
  }

  @Post('automated')
  @ApiOperation({
    summary: 'Trigger automated impact assessment',
    description: 'Creates and runs an automated impact assessment using AI algorithms',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Automated assessment completed successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Impact assessment already exists for this change request',
  })
  @ApiBody({ type: AutomatedAssessmentRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createAutomatedAssessment(
    @Body() requestDto: AutomatedAssessmentRequestDto,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.createAutomatedAssessment(requestDto, userId);
  }

  @Post('manual')
  @ApiOperation({
    summary: 'Create manual expert assessment',
    description: 'Creates an impact assessment based on expert manual input',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Manual assessment created successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Impact assessment already exists for this change request',
  })
  @ApiBody({ type: ManualAssessmentInputDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createManualAssessment(
    @Body() inputDto: ManualAssessmentInputDto,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.createManualAssessment(inputDto, userId);
  }

  @Post('bulk')
  @ApiOperation({
    summary: 'Bulk create impact assessments',
    description: 'Creates impact assessments for multiple change requests',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk assessments created successfully',
    type: ImpactAssessmentListResponseDto,
  })
  @ApiBody({ type: BulkAssessmentRequestDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkAssessment(
    @Body() bulkDto: BulkAssessmentRequestDto,
    @Request() req: any,
  ): Promise<ImpactAssessmentListResponseDto> {
    const userId = req.user.id;
    // TODO: Implement bulk assessment in service
    throw new Error('Bulk assessment not yet implemented');
  }

  @Get()
  @ApiOperation({
    summary: 'Get all impact assessments',
    description: 'Retrieves a paginated list of impact assessments with filtering options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Impact assessments retrieved successfully',
    type: ImpactAssessmentListResponseDto,
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 20)' })
  @ApiQuery({ name: 'status', required: false, enum: ImpactAssessmentStatus, description: 'Filter by status' })
  @ApiQuery({ name: 'method', required: false, enum: ImpactAssessmentMethod, description: 'Filter by method' })
  @ApiQuery({ name: 'impactLevel', required: false, enum: ImpactLevel, description: 'Filter by impact level' })
  @ApiQuery({ name: 'changeRequestId', required: false, type: String, description: 'Filter by change request' })
  @ApiQuery({ name: 'assessedById', required: false, type: String, description: 'Filter by assessor' })
  @ApiQuery({ name: 'requiresReview', required: false, type: Boolean, description: 'Filter assessments requiring review' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field (default: createdAt)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order (default: DESC)' })
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: ImpactAssessmentStatus,
    @Query('method') method?: ImpactAssessmentMethod,
    @Query('impactLevel') impactLevel?: ImpactLevel,
    @Query('changeRequestId') changeRequestId?: string,
    @Query('assessedById') assessedById?: string,
    @Query('requiresReview') requiresReview?: boolean,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
    @Request() req: any,
  ): Promise<ImpactAssessmentListResponseDto> {
    const userId = req.user.id;
    const options = {
      page,
      limit,
      status,
      method,
      impactLevel,
      changeRequestId,
      assessedById,
      requiresReview,
      sortBy,
      sortOrder,
    };
    // TODO: Implement findAll in service
    throw new Error('Find all assessments not yet implemented');
  }

  @Get('analytics')
  @ApiOperation({
    summary: 'Get impact assessment analytics',
    description: 'Retrieves analytics and performance metrics for impact assessments',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Analytics retrieved successfully',
    type: AssessmentAnalyticsDto,
  })
  @ApiQuery({ name: 'dateFrom', required: false, type: String, description: 'Filter from date (ISO string)' })
  @ApiQuery({ name: 'dateTo', required: false, type: String, description: 'Filter to date (ISO string)' })
  @ApiQuery({ name: 'method', required: false, enum: ImpactAssessmentMethod, description: 'Filter by method' })
  async getAnalytics(
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string,
    @Query('method') method?: ImpactAssessmentMethod,
    @Request() req: any,
  ): Promise<AssessmentAnalyticsDto> {
    const userId = req.user.id;
    const options = { dateFrom, dateTo, method };
    // TODO: Implement analytics in service
    throw new Error('Assessment analytics not yet implemented');
  }

  @Get('change-request/:changeRequestId')
  @ApiOperation({
    summary: 'Get assessment by change request ID',
    description: 'Retrieves the impact assessment for a specific change request',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Impact assessment retrieved successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Impact assessment not found for this change request',
  })
  @ApiParam({ name: 'changeRequestId', type: String, description: 'Change request ID' })
  async findByChangeRequestId(
    @Param('changeRequestId', ParseUUIDPipe) changeRequestId: string,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.findByChangeRequestId(changeRequestId, userId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get impact assessment by ID',
    description: 'Retrieves a specific impact assessment with full details',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Impact assessment retrieved successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Impact assessment not found',
  })
  @ApiParam({ name: 'id', type: String, description: 'Impact assessment ID' })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.findById(id, userId);
  }

  @Put(':id/complete')
  @ApiOperation({
    summary: 'Complete impact assessment',
    description: 'Marks an impact assessment as completed',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assessment completed successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Impact assessment not found',
  })
  @ApiParam({ name: 'id', type: String, description: 'Impact assessment ID' })
  async completeAssessment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    return this.impactAssessmentService.completeAssessment(id, userId);
  }

  @Get(':id/comparison')
  @ApiOperation({
    summary: 'Compare assessment with similar change requests',
    description: 'Compares the assessment with historical similar change requests',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Comparison data retrieved successfully',
    type: AssessmentComparisonDto,
  })
  @ApiParam({ name: 'id', type: String, description: 'Impact assessment ID' })
  async getComparison(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ): Promise<AssessmentComparisonDto> {
    const userId = req.user.id;
    // TODO: Implement comparison logic in service
    throw new Error('Assessment comparison not yet implemented');
  }

  @Post(':id/review')
  @ApiOperation({
    summary: 'Submit assessment review',
    description: 'Submits a review for an impact assessment',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Review submitted successfully',
    type: ImpactAssessmentResponseDto,
  })
  @ApiParam({ name: 'id', type: String, description: 'Impact assessment ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reviewComments: { type: 'string', description: 'Review comments' },
        qualityRating: { type: 'number', minimum: 1, maximum: 5, description: 'Quality rating (1-5)' },
        adjustments: {
          type: 'object',
          description: 'Suggested adjustments to the assessment',
        },
      },
      required: ['reviewComments'],
    },
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async submitReview(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() reviewData: {
      reviewComments: string;
      qualityRating?: number;
      adjustments?: Record<string, any>;
    },
    @Request() req: any,
  ): Promise<ImpactAssessmentResponseDto> {
    const userId = req.user.id;
    // TODO: Implement review submission in service
    throw new Error('Assessment review not yet implemented');
  }
}
