import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  OneToOne,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ChangeRequest } from './change-request.entity';
import {
  ImpactAssessmentStatus,
  ImpactAssessmentMethod,
  ImpactLevel,
  TimeImpactCategory,
  CostImpactCategory,
  ResourceImpactType,
  RiskImpactCategory,
  TechnicalRiskType,
  BusinessRiskType,
  ConfidenceLevel,
  AssessmentQuality,
  DEFAULT_IMPACT_ASSESSMENT_STATUS,
  DEFAULT_IMPACT_ASSESSMENT_METHOD,
  DEFAULT_IMPACT_LEVEL,
  DEFAULT_TIME_IMPACT_CATEGORY,
  DEFAULT_COST_IMPACT_CATEGORY,
  DEFAULT_RISK_IMPACT_CATEGORY,
  DEFAULT_CONFIDENCE_LEVEL,
  DEFAULT_ASSESSMENT_QUALITY,
} from '../enums';

/**
 * ImpactAssessment Entity
 * Sprint 7: Change Request Management System
 * 
 * Represents intelligent impact assessment for change requests with multi-dimensional analysis
 */
@Entity('impact_assessments')
export class ImpactAssessment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Change Request (One-to-One)
  @OneToOne(() => ChangeRequest, { nullable: false })
  @JoinColumn({ name: 'change_request_id' })
  changeRequest: ChangeRequest;

  @Column({ name: 'change_request_id', type: 'uuid', unique: true })
  @Index('idx_impact_assessments_change_request_id')
  changeRequestId: string;

  // Assessment Status and Method
  @Column({
    type: 'enum',
    enum: ImpactAssessmentStatus,
    default: DEFAULT_IMPACT_ASSESSMENT_STATUS,
  })
  @Index('idx_impact_assessments_status')
  status: ImpactAssessmentStatus;

  @Column({
    type: 'enum',
    enum: ImpactAssessmentMethod,
    default: DEFAULT_IMPACT_ASSESSMENT_METHOD,
  })
  method: ImpactAssessmentMethod;

  // Overall Impact Assessment
  @Column({
    type: 'enum',
    enum: ImpactLevel,
    default: DEFAULT_IMPACT_LEVEL,
  })
  overallImpact: ImpactLevel;

  @Column({
    type: 'enum',
    enum: ConfidenceLevel,
    default: DEFAULT_CONFIDENCE_LEVEL,
  })
  confidenceLevel: ConfidenceLevel;

  @Column({
    type: 'enum',
    enum: AssessmentQuality,
    default: DEFAULT_ASSESSMENT_QUALITY,
  })
  assessmentQuality: AssessmentQuality;

  // Time Impact Analysis
  @Column({
    type: 'enum',
    enum: TimeImpactCategory,
    default: DEFAULT_TIME_IMPACT_CATEGORY,
  })
  timeImpactCategory: TimeImpactCategory;

  @Column({
    name: 'estimated_delay_days',
    type: 'integer',
    default: 0,
  })
  estimatedDelayDays: number;

  @Column({
    name: 'min_delay_days',
    type: 'integer',
    default: 0,
  })
  minDelayDays: number;

  @Column({
    name: 'max_delay_days',
    type: 'integer',
    default: 0,
  })
  maxDelayDays: number;

  // Cost Impact Analysis
  @Column({
    type: 'enum',
    enum: CostImpactCategory,
    default: DEFAULT_COST_IMPACT_CATEGORY,
  })
  costImpactCategory: CostImpactCategory;

  @Column({
    name: 'estimated_additional_cost',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  estimatedAdditionalCost: number;

  @Column({
    name: 'min_additional_cost',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  minAdditionalCost: number;

  @Column({
    name: 'max_additional_cost',
    type: 'decimal',
    precision: 12,
    scale: 2,
    default: 0,
  })
  maxAdditionalCost: number;

  // Resource Impact Analysis
  @Column({
    type: 'jsonb',
    default: '[]',
    comment: 'Array of ResourceImpactType enums',
  })
  resourceImpactTypes: ResourceImpactType[];

  @Column({
    name: 'estimated_effort_hours',
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
  })
  estimatedEffortHours: number;

  @Column({
    name: 'min_effort_hours',
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
  })
  minEffortHours: number;

  @Column({
    name: 'max_effort_hours',
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
  })
  maxEffortHours: number;

  // Risk Impact Analysis
  @Column({
    type: 'enum',
    enum: RiskImpactCategory,
    default: DEFAULT_RISK_IMPACT_CATEGORY,
  })
  riskImpactCategory: RiskImpactCategory;

  @Column({
    type: 'jsonb',
    default: '[]',
    comment: 'Array of TechnicalRiskType enums',
  })
  technicalRisks: TechnicalRiskType[];

  @Column({
    type: 'jsonb',
    default: '[]',
    comment: 'Array of BusinessRiskType enums',
  })
  businessRisks: BusinessRiskType[];

  @Column({
    name: 'risk_mitigation_plan',
    type: 'text',
    nullable: true,
  })
  riskMitigationPlan: string;

  // Assessment Details
  @Column({ type: 'text', nullable: true })
  assessmentSummary: string;

  @Column({ type: 'text', nullable: true })
  assumptions: string;

  @Column({ type: 'text', nullable: true })
  limitations: string;

  @Column({ type: 'text', nullable: true })
  recommendations: string;

  // Assessment Metadata
  @Column({ type: 'jsonb', default: '{}' })
  assessmentData: {
    algorithmVersion?: string;
    inputParameters?: Record<string, any>;
    calculationDetails?: Record<string, any>;
    historicalComparisons?: Array<{
      changeRequestId: string;
      similarity: number;
      actualImpact: Record<string, any>;
    }>;
    expertReviews?: Array<{
      reviewerId: string;
      reviewDate: string;
      comments: string;
      adjustments: Record<string, any>;
    }>;
    validationResults?: Record<string, any>;
    [key: string]: any;
  };

  // Assessment Relationships
  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'assessed_by_id' })
  // assessedBy: User;

  @Column({ name: 'assessed_by_id', type: 'uuid', nullable: true })
  assessedById: string;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'reviewed_by_id' })
  // reviewedBy: User;

  @Column({ name: 'reviewed_by_id', type: 'uuid', nullable: true })
  reviewedById: string;

  // Timeline
  @Column({ name: 'assessment_started_at', type: 'timestamp', nullable: true })
  assessmentStartedAt: Date;

  @Column({ name: 'assessment_completed_at', type: 'timestamp', nullable: true })
  assessmentCompletedAt: Date;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'version', type: 'integer', default: 1 })
  version: number;

  // Computed Properties
  get assessmentDurationMinutes(): number | null {
    if (!this.assessmentStartedAt || !this.assessmentCompletedAt) return null;
    const diffMs = this.assessmentCompletedAt.getTime() - this.assessmentStartedAt.getTime();
    return Math.round(diffMs / (1000 * 60));
  }

  get costRange(): { min: number; max: number; estimated: number } {
    return {
      min: this.minAdditionalCost,
      max: this.maxAdditionalCost,
      estimated: this.estimatedAdditionalCost,
    };
  }

  get timeRange(): { min: number; max: number; estimated: number } {
    return {
      min: this.minDelayDays,
      max: this.maxDelayDays,
      estimated: this.estimatedDelayDays,
    };
  }

  get effortRange(): { min: number; max: number; estimated: number } {
    return {
      min: this.minEffortHours,
      max: this.maxEffortHours,
      estimated: this.estimatedEffortHours,
    };
  }

  get isHighImpact(): boolean {
    return [ImpactLevel.HIGH, ImpactLevel.SEVERE].includes(this.overallImpact);
  }

  get isHighRisk(): boolean {
    return [RiskImpactCategory.HIGH_RISK, RiskImpactCategory.CRITICAL_RISK].includes(this.riskImpactCategory);
  }

  get hasSignificantCostImpact(): boolean {
    return [CostImpactCategory.HIGH_COST, CostImpactCategory.VERY_HIGH_COST].includes(this.costImpactCategory);
  }

  get hasSignificantTimeImpact(): boolean {
    return [TimeImpactCategory.SIGNIFICANT_DELAY, TimeImpactCategory.MAJOR_DELAY, TimeImpactCategory.CRITICAL_DELAY].includes(this.timeImpactCategory);
  }

  get requiresManualReview(): boolean {
    return this.isHighImpact || this.isHighRisk || this.confidenceLevel === ConfidenceLevel.VERY_LOW;
  }
}
