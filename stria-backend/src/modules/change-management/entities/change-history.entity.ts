import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ChangeRequest } from './change-request.entity';
import {
  ChangeEventType,
  ChangeLogLevel,
  ChangeHistoryCategory,
  ActorType,
  ChangeTrackingStatus,
  VersionAction,
  DataChangeType,
  AuditImportance,
  DEFAULT_CHANGE_LOG_LEVEL,
  DEFAULT_CHANGE_HISTORY_CATEGORY,
  DEFAULT_ACTOR_TYPE,
  DEFAULT_CHANGE_TRACKING_STATUS,
  DEFAULT_AUDIT_IMPORTANCE,
} from '../enums';

/**
 * ChangeHistory Entity
 * Sprint 7: Change Request Management System
 * 
 * Represents the complete history and audit trail of change requests
 */
@Entity('change_histories')
export class ChangeHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Change Request
  @ManyToOne(() => ChangeRequest, { nullable: false })
  @JoinColumn({ name: 'change_request_id' })
  changeRequest: ChangeRequest;

  @Column({ name: 'change_request_id', type: 'uuid' })
  @Index('idx_change_histories_change_request_id')
  changeRequestId: string;

  // Event Classification
  @Column({
    type: 'enum',
    enum: ChangeEventType,
  })
  @Index('idx_change_histories_event_type')
  eventType: ChangeEventType;

  @Column({
    type: 'enum',
    enum: ChangeHistoryCategory,
    default: DEFAULT_CHANGE_HISTORY_CATEGORY,
  })
  category: ChangeHistoryCategory;

  @Column({
    type: 'enum',
    enum: AuditImportance,
    default: DEFAULT_AUDIT_IMPORTANCE,
  })
  importance: AuditImportance;

  // Event Details
  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  summary: string;

  // Actor Information
  @Column({
    type: 'enum',
    enum: ActorType,
    default: DEFAULT_ACTOR_TYPE,
  })
  actorType: ActorType;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'actor_user_id' })
  // actorUser: User;

  @Column({ name: 'actor_user_id', type: 'uuid', nullable: true })
  actorUserId: string;

  @Column({ name: 'actor_name', type: 'varchar', length: 255, nullable: true })
  actorName: string;

  @Column({ name: 'actor_identifier', type: 'varchar', length: 255, nullable: true })
  actorIdentifier: string;

  // Change Details
  @Column({ name: 'field_name', type: 'varchar', length: 100, nullable: true })
  fieldName: string;

  @Column({ name: 'old_value', type: 'text', nullable: true })
  oldValue: string;

  @Column({ name: 'new_value', type: 'text', nullable: true })
  newValue: string;

  @Column({
    type: 'enum',
    enum: DataChangeType,
    nullable: true,
  })
  changeType: DataChangeType;

  // Version Information
  @Column({ name: 'version_before', type: 'integer', nullable: true })
  versionBefore: number;

  @Column({ name: 'version_after', type: 'integer', nullable: true })
  versionAfter: number;

  @Column({
    type: 'enum',
    enum: VersionAction,
    nullable: true,
  })
  versionAction: VersionAction;

  // Context Information
  @Column({ name: 'session_id', type: 'varchar', length: 255, nullable: true })
  sessionId: string;

  @Column({ name: 'ip_address', type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  @Column({ name: 'request_id', type: 'varchar', length: 255, nullable: true })
  requestId: string;

  // Event Metadata
  @Column({ type: 'jsonb', default: '{}' })
  eventData: {
    source?: string;
    method?: string;
    endpoint?: string;
    parameters?: Record<string, any>;
    response?: Record<string, any>;
    errorDetails?: {
      code?: string;
      message?: string;
      stack?: string;
    };
    performanceMetrics?: {
      duration?: number;
      memoryUsage?: number;
      cpuUsage?: number;
    };
    businessContext?: {
      workflowId?: string;
      stepId?: string;
      approvalId?: string;
      notificationId?: string;
    };
    integrationData?: {
      externalSystem?: string;
      externalId?: string;
      syncStatus?: string;
    };
    [key: string]: any;
  };

  // Tracking Status
  @Column({
    type: 'enum',
    enum: ChangeTrackingStatus,
    default: DEFAULT_CHANGE_TRACKING_STATUS,
  })
  trackingStatus: ChangeTrackingStatus;

  // Timeline
  @Column({ name: 'event_timestamp', type: 'timestamp' })
  @Index('idx_change_histories_event_timestamp')
  eventTimestamp: Date;

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed Properties
  get isUserAction(): boolean {
    return this.actorType === ActorType.USER;
  }

  get isSystemAction(): boolean {
    return this.actorType === ActorType.SYSTEM;
  }

  get isHighImportance(): boolean {
    return [AuditImportance.HIGH, AuditImportance.CRITICAL].includes(this.importance);
  }

  get hasValueChange(): boolean {
    return this.oldValue !== null && this.newValue !== null && this.oldValue !== this.newValue;
  }

  get changeDescription(): string {
    if (this.hasValueChange) {
      return `${this.fieldName} changed from "${this.oldValue}" to "${this.newValue}"`;
    }
    return this.description || this.title;
  }
}

/**
 * ChangeLog Entity
 * Represents detailed log entries for change request operations
 */
@Entity('change_logs')
export class ChangeLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Change Request
  @ManyToOne(() => ChangeRequest, { nullable: false })
  @JoinColumn({ name: 'change_request_id' })
  changeRequest: ChangeRequest;

  @Column({ name: 'change_request_id', type: 'uuid' })
  @Index('idx_change_logs_change_request_id')
  changeRequestId: string;

  // Log Classification
  @Column({
    type: 'enum',
    enum: ChangeLogLevel,
    default: DEFAULT_CHANGE_LOG_LEVEL,
  })
  @Index('idx_change_logs_level')
  level: ChangeLogLevel;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index('idx_change_logs_component')
  component: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  operation: string;

  // Log Content
  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'jsonb', default: '{}' })
  details: {
    stackTrace?: string;
    errorCode?: string;
    correlationId?: string;
    duration?: number;
    inputData?: Record<string, any>;
    outputData?: Record<string, any>;
    warnings?: string[];
    metrics?: Record<string, number>;
    [key: string]: any;
  };

  // Context Information
  @Column({ name: 'correlation_id', type: 'varchar', length: 255, nullable: true })
  @Index('idx_change_logs_correlation_id')
  correlationId: string;

  @Column({ name: 'trace_id', type: 'varchar', length: 255, nullable: true })
  traceId: string;

  @Column({ name: 'span_id', type: 'varchar', length: 255, nullable: true })
  spanId: string;

  // Actor Information
  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'user_id' })
  // user: User;

  @Column({ name: 'user_id', type: 'uuid', nullable: true })
  userId: string;

  // Timeline
  @Column({ name: 'logged_at', type: 'timestamp' })
  @Index('idx_change_logs_logged_at')
  loggedAt: Date;

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Computed Properties
  get isError(): boolean {
    return [ChangeLogLevel.ERROR, ChangeLogLevel.CRITICAL].includes(this.level);
  }

  get isWarning(): boolean {
    return this.level === ChangeLogLevel.WARNING;
  }

  get isCritical(): boolean {
    return this.level === ChangeLogLevel.CRITICAL;
  }

  get hasStackTrace(): boolean {
    return !!(this.details?.stackTrace);
  }

  get formattedMessage(): string {
    const timestamp = this.loggedAt.toISOString();
    const levelStr = this.level.toUpperCase().padEnd(8);
    const component = this.component ? `[${this.component}]` : '';
    return `${timestamp} ${levelStr} ${component} ${this.message}`;
  }
}
