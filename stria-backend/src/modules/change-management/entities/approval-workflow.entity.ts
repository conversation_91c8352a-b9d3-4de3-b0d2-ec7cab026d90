import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ChangeRequest } from './change-request.entity';
import {
  WorkflowStatus,
  WorkflowType,
  ApprovalUrgency,
  WorkflowPriority,
  ApprovalStepStatus,
  ApprovalStepType,
  ApprovalDecision as ApprovalDecisionEnum,
  DEFAULT_WORKFLOW_STATUS,
  DEFAULT_WORKFLOW_TYPE,
  DEFAULT_APPROVAL_URGENCY,
  DEFAULT_WORKFLOW_PRIORITY,
  DEFAULT_APPROVAL_STEP_STATUS,
  DEFAULT_APPROVAL_STEP_TYPE,
} from '../enums';

/**
 * ApprovalWorkflow Entity
 * Sprint 7: Change Request Management System
 * 
 * Represents the overall approval workflow for a change request
 */
@Entity('approval_workflows')
export class ApprovalWorkflow {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Change Request (One-to-One)
  @OneToOne(() => ChangeRequest, { nullable: false })
  @JoinColumn({ name: 'change_request_id' })
  changeRequest: ChangeRequest;

  @Column({ name: 'change_request_id', type: 'uuid', unique: true })
  @Index('idx_approval_workflows_change_request_id')
  changeRequestId: string;

  // Workflow Configuration
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: WorkflowType,
    default: DEFAULT_WORKFLOW_TYPE,
  })
  workflowType: WorkflowType;

  @Column({
    type: 'enum',
    enum: WorkflowStatus,
    default: DEFAULT_WORKFLOW_STATUS,
  })
  @Index('idx_approval_workflows_status')
  status: WorkflowStatus;

  @Column({
    type: 'enum',
    enum: WorkflowPriority,
    default: DEFAULT_WORKFLOW_PRIORITY,
  })
  priority: WorkflowPriority;

  @Column({
    type: 'enum',
    enum: ApprovalUrgency,
    default: DEFAULT_APPROVAL_URGENCY,
  })
  urgency: ApprovalUrgency;

  // Workflow Creator and Owner
  // @ManyToOne(() => User, { nullable: false })
  // @JoinColumn({ name: 'created_by_id' })
  // createdBy: User;

  @Column({ name: 'created_by_id', type: 'uuid' })
  createdById: string;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'current_approver_id' })
  // currentApprover: User;

  @Column({ name: 'current_approver_id', type: 'uuid', nullable: true })
  currentApproverId: string;

  // Timeline Configuration
  @Column({ name: 'deadline', type: 'timestamp', nullable: true })
  deadline: Date;

  @Column({ name: 'estimated_duration_hours', type: 'integer', nullable: true })
  estimatedDurationHours: number;

  @Column({ name: 'auto_approve_after_hours', type: 'integer', nullable: true })
  autoApproveAfterHours: number;

  @Column({ name: 'escalation_after_hours', type: 'integer', nullable: true })
  escalationAfterHours: number;

  // Workflow Progress
  @Column({ name: 'current_step_order', type: 'integer', default: 1 })
  currentStepOrder: number;

  @Column({ name: 'total_steps', type: 'integer', default: 1 })
  totalSteps: number;

  @Column({ name: 'completed_steps', type: 'integer', default: 0 })
  completedSteps: number;

  // Workflow Results
  @Column({ name: 'final_decision', type: 'varchar', length: 50, nullable: true })
  finalDecision: string;

  @Column({ name: 'final_decision_reason', type: 'text', nullable: true })
  finalDecisionReason: string;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'final_approver_id' })
  // finalApprover: User;

  @Column({ name: 'final_approver_id', type: 'uuid', nullable: true })
  finalApproverId: string;

  // Timeline Tracking
  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ name: 'cancelled_at', type: 'timestamp', nullable: true })
  cancelledAt: Date;

  @Column({ name: 'last_activity_at', type: 'timestamp', nullable: true })
  lastActivityAt: Date;

  // Workflow Configuration and Metadata
  @Column({ type: 'jsonb', default: '{}' })
  configuration: {
    requireAllApprovals?: boolean;
    allowParallelApprovals?: boolean;
    enableEscalation?: boolean;
    enableDelegation?: boolean;
    enableAutoApproval?: boolean;
    notificationSettings?: {
      sendReminders?: boolean;
      reminderIntervalHours?: number;
      escalationNotifications?: boolean;
      completionNotifications?: boolean;
    };
    approvalConditions?: Array<{
      condition: string;
      required: boolean;
      description?: string;
    }>;
    customRules?: Array<{
      ruleId: string;
      condition: string;
      action: string;
      parameters?: Record<string, any>;
    }>;
    [key: string]: any;
  };

  // Workflow Metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    templateId?: string;
    templateVersion?: string;
    customFields?: Record<string, any>;
    integrationData?: Record<string, any>;
    performanceMetrics?: {
      averageApprovalTime?: number;
      escalationCount?: number;
      remindersSent?: number;
      delegationCount?: number;
    };
    auditTrail?: Array<{
      action: string;
      actor: string;
      timestamp: string;
      details?: Record<string, any>;
    }>;
    [key: string]: any;
  };

  // Relationships
  @OneToMany(() => ApprovalStep, step => step.workflow, { cascade: true })
  steps: ApprovalStep[];

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'version', type: 'integer', default: 1 })
  version: number;

  // Computed Properties
  get isActive(): boolean {
    return this.status === WorkflowStatus.ACTIVE;
  }

  get isCompleted(): boolean {
    return this.status === WorkflowStatus.COMPLETED;
  }

  get isCancelled(): boolean {
    return this.status === WorkflowStatus.CANCELLED;
  }

  get isOverdue(): boolean {
    if (!this.deadline) return false;
    return new Date() > this.deadline && !this.isCompleted && !this.isCancelled;
  }

  get progressPercentage(): number {
    if (this.totalSteps === 0) return 0;
    return Math.round((this.completedSteps / this.totalSteps) * 100);
  }

  get durationMinutes(): number | null {
    if (!this.startedAt) return null;
    const endTime = this.completedAt || new Date();
    return Math.round((endTime.getTime() - this.startedAt.getTime()) / (1000 * 60));
  }

  get hoursUntilDeadline(): number | null {
    if (!this.deadline) return null;
    const now = new Date();
    const diffMs = this.deadline.getTime() - now.getTime();
    return Math.round(diffMs / (1000 * 60 * 60));
  }

  get requiresEscalation(): boolean {
    if (!this.escalationAfterHours || !this.lastActivityAt) return false;
    const hoursSinceLastActivity = (new Date().getTime() - this.lastActivityAt.getTime()) / (1000 * 60 * 60);
    return hoursSinceLastActivity >= this.escalationAfterHours;
  }
}

/**
 * ApprovalStep Entity
 * Represents individual steps in the approval workflow
 */
@Entity('approval_steps')
export class ApprovalStep {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Workflow
  @ManyToOne(() => ApprovalWorkflow, workflow => workflow.steps, { nullable: false })
  @JoinColumn({ name: 'workflow_id' })
  workflow: ApprovalWorkflow;

  @Column({ name: 'workflow_id', type: 'uuid' })
  @Index('idx_approval_steps_workflow_id')
  workflowId: string;

  // Step Configuration
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'step_order', type: 'integer' })
  stepOrder: number;

  @Column({
    type: 'enum',
    enum: ApprovalStepStatus,
    default: DEFAULT_APPROVAL_STEP_STATUS,
  })
  @Index('idx_approval_steps_status')
  status: ApprovalStepStatus;

  @Column({
    type: 'enum',
    enum: ApprovalStepType,
    default: DEFAULT_APPROVAL_STEP_TYPE,
  })
  stepType: ApprovalStepType;

  // Approver Assignment
  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'assigned_to_id' })
  // assignedTo: User;

  @Column({ name: 'assigned_to_id', type: 'uuid', nullable: true })
  assignedToId: string;

  @Column({ name: 'assigned_role', type: 'varchar', length: 100, nullable: true })
  assignedRole: string;

  // Step Timeline
  @Column({ name: 'due_date', type: 'timestamp', nullable: true })
  dueDate: Date;

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
  completedAt: Date;

  // Step Configuration
  @Column({ type: 'jsonb', default: '{}' })
  configuration: {
    isRequired?: boolean;
    canDelegate?: boolean;
    canSkip?: boolean;
    requiresComment?: boolean;
    allowedDecisions?: string[];
    conditions?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    [key: string]: any;
  };

  // Relationships
  @OneToMany(() => ApprovalDecision, decision => decision.step, { cascade: true })
  decisions: ApprovalDecision[];

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}

/**
 * ApprovalDecision Entity
 * Represents decisions made at each approval step
 */
@Entity('approval_decisions')
export class ApprovalDecision {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Relationship to Step
  @ManyToOne(() => ApprovalStep, step => step.decisions, { nullable: false })
  @JoinColumn({ name: 'step_id' })
  step: ApprovalStep;

  @Column({ name: 'step_id', type: 'uuid' })
  @Index('idx_approval_decisions_step_id')
  stepId: string;

  // Decision Details
  @Column({
    type: 'enum',
    enum: ApprovalDecisionEnum,
  })
  decision: ApprovalDecisionEnum;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({ type: 'text', nullable: true })
  conditions: string;

  // Decision Maker
  // @ManyToOne(() => User, { nullable: false })
  // @JoinColumn({ name: 'decided_by_id' })
  // decidedBy: User;

  @Column({ name: 'decided_by_id', type: 'uuid' })
  decidedById: string;

  @Column({ name: 'decided_at', type: 'timestamp' })
  decidedAt: Date;

  // Delegation Information
  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'delegated_to_id' })
  // delegatedTo: User;

  @Column({ name: 'delegated_to_id', type: 'uuid', nullable: true })
  delegatedToId: string;

  @Column({ name: 'delegation_reason', type: 'text', nullable: true })
  delegationReason: string;

  // Decision Metadata
  @Column({ type: 'jsonb', default: '{}' })
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    location?: string;
    attachments?: Array<{
      id: string;
      name: string;
      url: string;
    }>;
    digitalSignature?: {
      signature: string;
      algorithm: string;
      timestamp: string;
    };
    [key: string]: any;
  };

  // Audit Fields
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
