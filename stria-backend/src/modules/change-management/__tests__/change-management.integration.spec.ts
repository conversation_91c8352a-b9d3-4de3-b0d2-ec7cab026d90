import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { JwtModule } from '@nestjs/jwt';
import { ChangeManagementModule } from '../change-management.module';
import { ChangeRequest } from '../entities/change-request.entity';
import { ImpactAssessment } from '../entities/impact-assessment.entity';
import { ApprovalWorkflow, ApprovalStep, ApprovalDecision } from '../entities/approval-workflow.entity';
import { ChangeHistory } from '../entities/change-history.entity';
import { User } from '../../users/entities/user.entity';
import { Project } from '../../projects/entities/project.entity';
import { ProjectBlueprint } from '../../projects/entities/project-blueprint.entity';
import { UserRole, UserStatus } from '../../users/enums';
import { ProjectStatus } from '../../projects/enums';
import {
  ChangeRequestStatus,
  ChangeRequestType,
  ChangeRequestPriority,
  ChangeRequestComplexity,
  ChangeRequestImpactArea,
  ImpactAssessmentMethod,
  WorkflowType,
} from '../enums';

/**
 * Change Management Integration Tests
 * Sprint 7: Change Request Management System
 * 
 * End-to-end integration tests for the complete change management workflow
 */
describe('Change Management Integration (e2e)', () => {
  let app: INestApplication;
  let changeRequestRepository: Repository<ChangeRequest>;
  let impactAssessmentRepository: Repository<ImpactAssessment>;
  let workflowRepository: Repository<ApprovalWorkflow>;
  let userRepository: Repository<User>;
  let projectRepository: Repository<Project>;
  let jwtService: JwtService;

  // Test data
  let testUser: User;
  let testProject: Project;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: 'localhost',
          port: 5433, // Test database port from docker-compose.test.yml
          username: 'postgres',
          password: 'password',
          database: 'stria_test',
          entities: [
            ChangeRequest,
            ImpactAssessment,
            ApprovalWorkflow,
            ApprovalStep,
            ApprovalDecision,
            ChangeHistory,
            User,
            Project,
            ProjectBlueprint,
          ],
          synchronize: true,
          logging: false,
          dropSchema: true, // Clean database for each test run
        }),
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        ChangeManagementModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get repositories
    changeRequestRepository = moduleFixture.get<Repository<ChangeRequest>>(
      getRepositoryToken(ChangeRequest),
    );
    impactAssessmentRepository = moduleFixture.get<Repository<ImpactAssessment>>(
      getRepositoryToken(ImpactAssessment),
    );
    workflowRepository = moduleFixture.get<Repository<ApprovalWorkflow>>(
      getRepositoryToken(ApprovalWorkflow),
    );
    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    projectRepository = moduleFixture.get<Repository<Project>>(
      getRepositoryToken(Project),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Clean up data between tests
    await changeRequestRepository.clear();
    await impactAssessmentRepository.clear();
    await workflowRepository.clear();
  });

  async function setupTestData() {
    // Create test user
    testUser = userRepository.create({
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      passwordHash: 'hashedpassword',
      role: UserRole.CLIENT,
      status: UserStatus.ACTIVE,
    });
    await userRepository.save(testUser);

    // Create test project
    testProject = projectRepository.create({
      name: 'Test Project',
      description: 'Test project for integration tests',
      clientId: testUser.id,
      status: ProjectStatus.IN_PROGRESS,
    });
    await projectRepository.save(testProject);

    // Generate auth token
    authToken = jwtService.sign({ sub: testUser.id, email: testUser.email });
  }

  describe('Complete Change Request Workflow', () => {
    it('should handle complete change request lifecycle', async () => {
      // Step 1: Create change request
      const createChangeRequestDto = {
        title: 'Add User Dashboard',
        description: 'Implement a comprehensive user dashboard with analytics',
        type: ChangeRequestType.FEATURE_ADDITION,
        priority: ChangeRequestPriority.HIGH,
        complexity: ChangeRequestComplexity.MAJOR,
        projectId: testProject.id,
        impactAreas: [
          ChangeRequestImpactArea.FRONTEND,
          ChangeRequestImpactArea.BACKEND,
          ChangeRequestImpactArea.DATABASE,
        ],
        businessJustification: 'Users need better visibility into their data',
        technicalDetails: 'React dashboard with real-time data visualization',
        estimatedCost: 15000,
        estimatedHours: 120,
        testingRequirements: 'Unit tests, integration tests, E2E tests',
        rollbackPlan: 'Feature flag rollback available',
      };

      const createResponse = await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send(createChangeRequestDto)
        .expect(201);

      const changeRequestId = createResponse.body.id;
      expect(changeRequestId).toBeDefined();
      expect(createResponse.body.status).toBe(ChangeRequestStatus.DRAFT);

      // Step 2: Submit change request
      await request(app.getHttpServer())
        .put(`/change-requests/${changeRequestId}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: ChangeRequestStatus.SUBMITTED,
          reason: 'Ready for review',
        })
        .expect(200);

      // Step 3: Create automated impact assessment
      const assessmentResponse = await request(app.getHttpServer())
        .post('/impact-assessments/automated')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          useHistoricalData: true,
          useMachineLearning: true,
          includeRiskAnalysis: true,
          confidenceThreshold: 0.7,
        })
        .expect(201);

      expect(assessmentResponse.body.method).toBe(ImpactAssessmentMethod.AUTOMATED);
      expect(assessmentResponse.body.overallImpact).toBeDefined();
      expect(assessmentResponse.body.confidenceLevel).toBeDefined();

      // Step 4: Create approval workflow
      const workflowResponse = await request(app.getHttpServer())
        .post('/approval-workflows/quick')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
          approverIds: [testUser.id],
          priority: 'high',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          requireAllApprovals: true,
          enableEscalation: true,
          escalationAfterHours: 24,
        })
        .expect(201);

      const workflowId = workflowResponse.body.id;
      expect(workflowId).toBeDefined();
      expect(workflowResponse.body.workflowType).toBe(WorkflowType.SEQUENTIAL_APPROVAL);

      // Step 5: Start workflow
      await request(app.getHttpServer())
        .put(`/approval-workflows/${workflowId}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Step 6: Process approval decision
      const workflowDetails = await request(app.getHttpServer())
        .get(`/approval-workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const firstStep = workflowDetails.body.steps[0];
      
      await request(app.getHttpServer())
        .post(`/approval-workflows/${workflowId}/steps/${firstStep.id}/decision`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          decision: 'approve',
          comments: 'Looks good, approved for implementation',
          conditions: 'Complete testing before deployment',
        })
        .expect(200);

      // Step 7: Verify final state
      const finalChangeRequest = await request(app.getHttpServer())
        .get(`/change-requests/${changeRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(finalChangeRequest.body.status).toBe(ChangeRequestStatus.APPROVED);

      const finalWorkflow = await request(app.getHttpServer())
        .get(`/approval-workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(finalWorkflow.body.status).toBe('completed');
      expect(finalWorkflow.body.finalDecision).toBe('approved');
    });

    it('should handle change request rejection workflow', async () => {
      // Create and submit change request
      const createResponse = await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Unnecessary Feature',
          description: 'A feature that is not needed',
          type: ChangeRequestType.FEATURE_ADDITION,
          priority: ChangeRequestPriority.LOW,
          complexity: ChangeRequestComplexity.MINOR,
          projectId: testProject.id,
          impactAreas: [ChangeRequestImpactArea.FRONTEND],
          businessJustification: 'Weak justification',
          technicalDetails: 'Simple implementation',
        })
        .expect(201);

      const changeRequestId = createResponse.body.id;

      // Submit for review
      await request(app.getHttpServer())
        .put(`/change-requests/${changeRequestId}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: ChangeRequestStatus.SUBMITTED,
        })
        .expect(200);

      // Create workflow
      const workflowResponse = await request(app.getHttpServer())
        .post('/approval-workflows/quick')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
          approverIds: [testUser.id],
        })
        .expect(201);

      const workflowId = workflowResponse.body.id;

      // Start workflow
      await request(app.getHttpServer())
        .put(`/approval-workflows/${workflowId}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Reject the change request
      const workflowDetails = await request(app.getHttpServer())
        .get(`/approval-workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const firstStep = workflowDetails.body.steps[0];

      await request(app.getHttpServer())
        .post(`/approval-workflows/${workflowId}/steps/${firstStep.id}/decision`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          decision: 'reject',
          comments: 'Insufficient business justification',
        })
        .expect(200);

      // Verify rejection
      const finalChangeRequest = await request(app.getHttpServer())
        .get(`/change-requests/${changeRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(finalChangeRequest.body.status).toBe(ChangeRequestStatus.REJECTED);
    });
  });

  describe('Impact Assessment Integration', () => {
    let changeRequestId: string;

    beforeEach(async () => {
      // Create a change request for assessment tests
      const createResponse = await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Performance Optimization',
          description: 'Optimize database queries and API responses',
          type: ChangeRequestType.PERFORMANCE_OPTIMIZATION,
          priority: ChangeRequestPriority.MEDIUM,
          complexity: ChangeRequestComplexity.MODERATE,
          projectId: testProject.id,
          impactAreas: [ChangeRequestImpactArea.BACKEND, ChangeRequestImpactArea.DATABASE],
          businessJustification: 'Improve user experience with faster load times',
          technicalDetails: 'Query optimization and caching implementation',
          estimatedCost: 8000,
          estimatedHours: 60,
        })
        .expect(201);

      changeRequestId = createResponse.body.id;
    });

    it('should create and complete automated assessment', async () => {
      // Create automated assessment
      const assessmentResponse = await request(app.getHttpServer())
        .post('/impact-assessments/automated')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          useHistoricalData: true,
          useMachineLearning: true,
          includeRiskAnalysis: true,
        })
        .expect(201);

      const assessmentId = assessmentResponse.body.id;
      expect(assessmentResponse.body.method).toBe(ImpactAssessmentMethod.AUTOMATED);
      expect(assessmentResponse.body.status).toBe('completed');

      // Get assessment details
      const assessmentDetails = await request(app.getHttpServer())
        .get(`/impact-assessments/${assessmentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(assessmentDetails.body.overallImpact).toBeDefined();
      expect(assessmentDetails.body.timeImpactCategory).toBeDefined();
      expect(assessmentDetails.body.costImpactCategory).toBeDefined();
      expect(assessmentDetails.body.riskImpactCategory).toBeDefined();

      // Get assessment by change request ID
      const assessmentByChangeRequest = await request(app.getHttpServer())
        .get(`/impact-assessments/change-request/${changeRequestId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(assessmentByChangeRequest.body.id).toBe(assessmentId);
    });

    it('should create manual assessment', async () => {
      // Create manual assessment
      const manualAssessmentResponse = await request(app.getHttpServer())
        .post('/impact-assessments/manual')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          assessmentSummary: 'Expert manual assessment of performance optimization',
          overallImpact: 'medium',
          confidenceLevel: 'very_high',
          timeImpactCategory: 'moderate_delay',
          estimatedDelayDays: 7,
          minDelayDays: 5,
          maxDelayDays: 10,
          costImpactCategory: 'moderate_cost',
          estimatedAdditionalCost: 3000,
          minAdditionalCost: 2000,
          maxAdditionalCost: 4000,
          riskImpactCategory: 'low_risk',
          technicalRisks: ['performance_regression'],
          businessRisks: [],
          riskMitigationPlan: 'Comprehensive testing and gradual rollout',
          expertNotes: 'Well-defined optimization with clear benefits',
          reviewRequired: false,
        })
        .expect(201);

      expect(manualAssessmentResponse.body.method).toBe(ImpactAssessmentMethod.EXPERT_MANUAL);
      expect(manualAssessmentResponse.body.overallImpact).toBe('medium');
      expect(manualAssessmentResponse.body.confidenceLevel).toBe('very_high');
    });
  });

  describe('Workflow Management Integration', () => {
    let changeRequestId: string;

    beforeEach(async () => {
      // Create and submit change request
      const createResponse = await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Security Enhancement',
          description: 'Implement additional security measures',
          type: ChangeRequestType.SECURITY_ENHANCEMENT,
          priority: ChangeRequestPriority.HIGH,
          complexity: ChangeRequestComplexity.MAJOR,
          projectId: testProject.id,
          impactAreas: [ChangeRequestImpactArea.SECURITY, ChangeRequestImpactArea.BACKEND],
          businessJustification: 'Compliance requirement',
          technicalDetails: 'OAuth 2.0 and additional encryption',
        })
        .expect(201);

      changeRequestId = createResponse.body.id;

      await request(app.getHttpServer())
        .put(`/change-requests/${changeRequestId}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ status: ChangeRequestStatus.SUBMITTED })
        .expect(200);
    });

    it('should create and manage sequential approval workflow', async () => {
      // Create sequential workflow
      const workflowResponse = await request(app.getHttpServer())
        .post('/approval-workflows/quick')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
          approverIds: [testUser.id],
          priority: 'high',
          requireAllApprovals: true,
        })
        .expect(201);

      const workflowId = workflowResponse.body.id;

      // Get workflow details
      const workflowDetails = await request(app.getHttpServer())
        .get(`/approval-workflows/${workflowId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(workflowDetails.body.workflowType).toBe(WorkflowType.SEQUENTIAL_APPROVAL);
      expect(workflowDetails.body.status).toBe('draft');
      expect(workflowDetails.body.steps).toHaveLength(1);

      // Start workflow
      const startResponse = await request(app.getHttpServer())
        .put(`/approval-workflows/${workflowId}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(startResponse.body.status).toBe('active');

      // Get workflow summary
      const summaryResponse = await request(app.getHttpServer())
        .get(`/approval-workflows/${workflowId}/summary`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(summaryResponse.body.progressPercentage).toBeDefined();
      expect(summaryResponse.body.currentStepOrder).toBe(1);
    });

    it('should handle workflow cancellation', async () => {
      // Create workflow
      const workflowResponse = await request(app.getHttpServer())
        .post('/approval-workflows/quick')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          changeRequestId,
          workflowType: WorkflowType.SEQUENTIAL_APPROVAL,
          approverIds: [testUser.id],
        })
        .expect(201);

      const workflowId = workflowResponse.body.id;

      // Start workflow
      await request(app.getHttpServer())
        .put(`/approval-workflows/${workflowId}/start`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Cancel workflow
      const cancelResponse = await request(app.getHttpServer())
        .put(`/approval-workflows/${workflowId}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          reason: 'Requirements changed',
        })
        .expect(200);

      expect(cancelResponse.body.status).toBe('cancelled');
      expect(cancelResponse.body.finalDecisionReason).toBe('Requirements changed');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid change request creation', async () => {
      await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: '', // Invalid empty title
          type: 'invalid_type', // Invalid type
          projectId: 'non-existent-project', // Non-existent project
        })
        .expect(400);
    });

    it('should handle unauthorized access', async () => {
      await request(app.getHttpServer())
        .get('/change-requests')
        .expect(401);
    });

    it('should handle not found resources', async () => {
      await request(app.getHttpServer())
        .get('/change-requests/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should handle duplicate assessment creation', async () => {
      // Create change request
      const createResponse = await request(app.getHttpServer())
        .post('/change-requests')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Test Change',
          type: ChangeRequestType.FEATURE_ADDITION,
          projectId: testProject.id,
          impactAreas: [ChangeRequestImpactArea.FRONTEND],
        })
        .expect(201);

      const changeRequestId = createResponse.body.id;

      // Create first assessment
      await request(app.getHttpServer())
        .post('/impact-assessments/automated')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ changeRequestId })
        .expect(201);

      // Try to create duplicate assessment
      await request(app.getHttpServer())
        .post('/impact-assessments/automated')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ changeRequestId })
        .expect(409); // Conflict
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle bulk operations efficiently', async () => {
      const bulkCreateDto = {
        projectId: testProject.id,
        changeRequests: Array.from({ length: 10 }, (_, i) => ({
          title: `Bulk Change Request ${i + 1}`,
          description: `Description for bulk change ${i + 1}`,
          type: ChangeRequestType.FEATURE_ADDITION,
          priority: ChangeRequestPriority.MEDIUM,
          complexity: ChangeRequestComplexity.MINOR,
          impactAreas: [ChangeRequestImpactArea.FRONTEND],
        })),
        batchName: 'Performance Test Batch',
      };

      const startTime = Date.now();
      
      const bulkResponse = await request(app.getHttpServer())
        .post('/change-requests/bulk')
        .set('Authorization', `Bearer ${authToken}`)
        .send(bulkCreateDto)
        .expect(201);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      expect(bulkResponse.body.items).toHaveLength(10);
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle pagination correctly', async () => {
      // Create multiple change requests
      for (let i = 0; i < 25; i++) {
        await request(app.getHttpServer())
          .post('/change-requests')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            title: `Change Request ${i + 1}`,
            type: ChangeRequestType.FEATURE_ADDITION,
            projectId: testProject.id,
            impactAreas: [ChangeRequestImpactArea.FRONTEND],
          })
          .expect(201);
      }

      // Test pagination
      const page1Response = await request(app.getHttpServer())
        .get('/change-requests?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(page1Response.body.items).toHaveLength(10);
      expect(page1Response.body.page).toBe(1);
      expect(page1Response.body.totalPages).toBe(3);
      expect(page1Response.body.hasNext).toBe(true);

      const page2Response = await request(app.getHttpServer())
        .get('/change-requests?page=2&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(page2Response.body.items).toHaveLength(10);
      expect(page2Response.body.page).toBe(2);
      expect(page2Response.body.hasNext).toBe(true);
      expect(page2Response.body.hasPrev).toBe(true);
    });
  });
});
