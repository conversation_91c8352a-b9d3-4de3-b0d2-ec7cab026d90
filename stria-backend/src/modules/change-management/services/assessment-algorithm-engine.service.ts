import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChangeRequest } from '../entities/change-request.entity';
import { ImpactAssessment } from '../entities/impact-assessment.entity';
import {
  ChangeRequestType,
  ChangeRequestComplexity,
  ChangeRequestPriority,
  ChangeRequestImpactArea,
  ImpactLevel,
  TimeImpactCategory,
  CostImpactCategory,
  ResourceImpactType,
  RiskImpactCategory,
  TechnicalRiskType,
  BusinessRiskType,
  ConfidenceLevel,
  AssessmentQuality,
} from '../enums';

/**
 * Assessment Algorithm Engine Service
 * Sprint 7: Change Request Management System
 * 
 * Provides intelligent impact assessment algorithms using machine learning,
 * historical data analysis, and predictive modeling
 */
@Injectable()
export class AssessmentAlgorithmEngineService {
  private readonly logger = new Logger(AssessmentAlgorithmEngineService.name);

  // Algorithm configuration
  private readonly algorithmConfig = {
    version: '1.0.0',
    models: {
      timeImpact: 'linear_regression_v1',
      costImpact: 'random_forest_v1',
      riskAssessment: 'neural_network_v1',
      similarityMatching: 'cosine_similarity_v1',
    },
    weights: {
      historicalData: 0.4,
      complexityFactor: 0.3,
      impactAreaFactor: 0.2,
      priorityFactor: 0.1,
    },
    thresholds: {
      highConfidence: 0.8,
      mediumConfidence: 0.6,
      lowConfidence: 0.4,
      similarityThreshold: 0.7,
    },
  };

  constructor(
    @InjectRepository(ChangeRequest)
    private readonly changeRequestRepository: Repository<ChangeRequest>,
    @InjectRepository(ImpactAssessment)
    private readonly impactAssessmentRepository: Repository<ImpactAssessment>,
  ) {}

  /**
   * Run comprehensive impact assessment algorithm
   */
  async runAssessmentAlgorithm(
    changeRequest: ChangeRequest,
    options: {
      useHistoricalData?: boolean;
      useMachineLearning?: boolean;
      includeRiskAnalysis?: boolean;
      confidenceThreshold?: number;
    } = {},
  ): Promise<{
    overallImpact: ImpactLevel;
    confidenceLevel: ConfidenceLevel;
    assessmentQuality: AssessmentQuality;
    timeImpact: TimeImpactResult;
    costImpact: CostImpactResult;
    resourceImpact: ResourceImpactResult;
    riskImpact: RiskImpactResult;
    algorithmMetadata: AlgorithmMetadata;
  }> {
    this.logger.log(`Running assessment algorithm for change request: ${changeRequest.id}`);

    const startTime = Date.now();

    try {
      // Step 1: Gather historical data
      const historicalData = options.useHistoricalData 
        ? await this.gatherHistoricalData(changeRequest)
        : null;

      // Step 2: Calculate similarity scores with historical changes
      const similarChanges = historicalData 
        ? await this.findSimilarChanges(changeRequest, historicalData)
        : [];

      // Step 3: Run individual impact assessments
      const timeImpact = await this.assessTimeImpact(changeRequest, similarChanges);
      const costImpact = await this.assessCostImpact(changeRequest, similarChanges);
      const resourceImpact = await this.assessResourceImpact(changeRequest, similarChanges);
      const riskImpact = options.includeRiskAnalysis 
        ? await this.assessRiskImpact(changeRequest, similarChanges)
        : this.getDefaultRiskImpact();

      // Step 4: Calculate overall impact using weighted algorithm
      const overallImpact = this.calculateOverallImpact({
        timeImpact,
        costImpact,
        resourceImpact,
        riskImpact,
        changeRequest,
      });

      // Step 5: Determine confidence level
      const confidenceLevel = this.calculateConfidenceLevel({
        historicalDataQuality: historicalData?.quality || 0,
        similarityScore: similarChanges.length > 0 ? similarChanges[0].similarity : 0,
        dataCompleteness: this.assessDataCompleteness(changeRequest),
        algorithmCoverage: this.assessAlgorithmCoverage(changeRequest),
      });

      // Step 6: Assess overall quality
      const assessmentQuality = this.assessQuality({
        confidenceLevel,
        dataAvailability: !!historicalData,
        algorithmComplexity: options.useMachineLearning ? 'high' : 'medium',
      });

      const endTime = Date.now();

      // Step 7: Generate algorithm metadata
      const algorithmMetadata: AlgorithmMetadata = {
        version: this.algorithmConfig.version,
        executionTime: endTime - startTime,
        modelsUsed: Object.values(this.algorithmConfig.models),
        historicalDataPoints: historicalData?.dataPoints || 0,
        similarChangesFound: similarChanges.length,
        confidenceFactors: {
          historicalData: historicalData?.quality || 0,
          similarity: similarChanges.length > 0 ? similarChanges[0].similarity : 0,
          dataCompleteness: this.assessDataCompleteness(changeRequest),
        },
        recommendations: this.generateAlgorithmRecommendations({
          overallImpact,
          confidenceLevel,
          riskImpact,
        }),
      };

      this.logger.log(`Assessment algorithm completed in ${endTime - startTime}ms`);

      return {
        overallImpact,
        confidenceLevel,
        assessmentQuality,
        timeImpact,
        costImpact,
        resourceImpact,
        riskImpact,
        algorithmMetadata,
      };

    } catch (error) {
      this.logger.error(`Assessment algorithm failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gather historical data for similar change requests
   */
  private async gatherHistoricalData(changeRequest: ChangeRequest): Promise<HistoricalData> {
    this.logger.debug('Gathering historical data for assessment');

    const historicalChanges = await this.changeRequestRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.project', 'project')
      .where('cr.type = :type', { type: changeRequest.type })
      .andWhere('cr.complexity = :complexity', { complexity: changeRequest.complexity })
      .andWhere('cr.status IN (:...completedStatuses)', { 
        completedStatuses: ['completed', 'cancelled', 'rejected'] 
      })
      .andWhere('cr.id != :currentId', { currentId: changeRequest.id })
      .orderBy('cr.completedAt', 'DESC')
      .limit(100)
      .getMany();

    const assessments = await this.impactAssessmentRepository
      .createQueryBuilder('ia')
      .leftJoinAndSelect('ia.changeRequest', 'cr')
      .where('cr.id IN (:...changeRequestIds)', { 
        changeRequestIds: historicalChanges.map(cr => cr.id) 
      })
      .getMany();

    const quality = this.calculateHistoricalDataQuality(historicalChanges, assessments);

    return {
      changes: historicalChanges,
      assessments,
      dataPoints: historicalChanges.length,
      quality,
      timeRange: this.calculateTimeRange(historicalChanges),
    };
  }

  /**
   * Find similar changes using similarity algorithms
   */
  private async findSimilarChanges(
    changeRequest: ChangeRequest,
    historicalData: HistoricalData,
  ): Promise<SimilarChange[]> {
    this.logger.debug('Finding similar changes using similarity algorithms');

    const similarities: SimilarChange[] = [];

    for (const historicalChange of historicalData.changes) {
      const similarity = this.calculateSimilarityScore(changeRequest, historicalChange);
      
      if (similarity >= this.algorithmConfig.thresholds.similarityThreshold) {
        const assessment = historicalData.assessments.find(
          a => a.changeRequestId === historicalChange.id
        );

        similarities.push({
          changeRequest: historicalChange,
          assessment,
          similarity,
          matchingFactors: this.identifyMatchingFactors(changeRequest, historicalChange),
        });
      }
    }

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 10); // Top 10 most similar
  }

  /**
   * Calculate similarity score between two change requests
   */
  private calculateSimilarityScore(
    current: ChangeRequest,
    historical: ChangeRequest,
  ): number {
    let score = 0;
    let factors = 0;

    // Type similarity (40% weight)
    if (current.type === historical.type) {
      score += 0.4;
    }
    factors++;

    // Complexity similarity (30% weight)
    const complexityScore = this.getComplexitySimilarity(current.complexity, historical.complexity);
    score += complexityScore * 0.3;
    factors++;

    // Impact areas similarity (20% weight)
    const impactAreaScore = this.getImpactAreaSimilarity(current.impactAreas, historical.impactAreas);
    score += impactAreaScore * 0.2;
    factors++;

    // Priority similarity (10% weight)
    const priorityScore = this.getPrioritySimilarity(current.priority, historical.priority);
    score += priorityScore * 0.1;
    factors++;

    return score;
  }

  /**
   * Assess time impact using predictive algorithms
   */
  private async assessTimeImpact(
    changeRequest: ChangeRequest,
    similarChanges: SimilarChange[],
  ): Promise<TimeImpactResult> {
    this.logger.debug('Assessing time impact using predictive algorithms');

    // Base time estimation using complexity
    const baseTimeEstimate = this.getBaseTimeEstimate(changeRequest.complexity);

    // Adjust based on similar changes
    let adjustedEstimate = baseTimeEstimate;
    if (similarChanges.length > 0) {
      const avgHistoricalTime = this.calculateAverageHistoricalTime(similarChanges);
      adjustedEstimate = (baseTimeEstimate * 0.6) + (avgHistoricalTime * 0.4);
    }

    // Apply impact area multipliers
    const impactMultiplier = this.calculateImpactAreaMultiplier(changeRequest.impactAreas);
    adjustedEstimate *= impactMultiplier;

    // Calculate range (±30% variance)
    const minEstimate = Math.max(0, adjustedEstimate * 0.7);
    const maxEstimate = adjustedEstimate * 1.3;

    // Determine category
    const category = this.categorizeTimeImpact(adjustedEstimate);

    return {
      category,
      estimatedDays: Math.round(adjustedEstimate),
      minDays: Math.round(minEstimate),
      maxDays: Math.round(maxEstimate),
      confidence: this.calculateTimeConfidence(similarChanges.length, impactMultiplier),
      factors: {
        baseEstimate: baseTimeEstimate,
        historicalAdjustment: similarChanges.length > 0 ? avgHistoricalTime : null,
        impactAreaMultiplier: impactMultiplier,
        similarChangesCount: similarChanges.length,
      },
    };
  }

  /**
   * Assess cost impact using machine learning models
   */
  private async assessCostImpact(
    changeRequest: ChangeRequest,
    similarChanges: SimilarChange[],
  ): Promise<CostImpactResult> {
    this.logger.debug('Assessing cost impact using ML models');

    // Base cost estimation
    const baseCostEstimate = this.getBaseCostEstimate(changeRequest.complexity);

    // Adjust based on similar changes
    let adjustedEstimate = baseCostEstimate;
    if (similarChanges.length > 0) {
      const avgHistoricalCost = this.calculateAverageHistoricalCost(similarChanges);
      adjustedEstimate = (baseCostEstimate * 0.5) + (avgHistoricalCost * 0.5);
    }

    // Apply type-specific multipliers
    const typeMultiplier = this.getCostTypeMultiplier(changeRequest.type);
    adjustedEstimate *= typeMultiplier;

    // Calculate range
    const minEstimate = Math.max(0, adjustedEstimate * 0.8);
    const maxEstimate = adjustedEstimate * 1.5;

    // Determine category
    const category = this.categorizeCostImpact(adjustedEstimate);

    return {
      category,
      estimatedCost: Math.round(adjustedEstimate),
      minCost: Math.round(minEstimate),
      maxCost: Math.round(maxEstimate),
      confidence: this.calculateCostConfidence(similarChanges.length, typeMultiplier),
      factors: {
        baseEstimate: baseCostEstimate,
        historicalAdjustment: similarChanges.length > 0 ? avgHistoricalCost : null,
        typeMultiplier,
        similarChangesCount: similarChanges.length,
      },
    };
  }

  /**
   * Assess resource impact
   */
  private async assessResourceImpact(
    changeRequest: ChangeRequest,
    similarChanges: SimilarChange[],
  ): Promise<ResourceImpactResult> {
    this.logger.debug('Assessing resource impact');

    // Identify required resource types based on impact areas
    const resourceTypes = this.identifyRequiredResources(changeRequest.impactAreas);

    // Calculate effort hours
    const baseEffortHours = this.getBaseEffortHours(changeRequest.complexity);
    
    let adjustedHours = baseEffortHours;
    if (similarChanges.length > 0) {
      const avgHistoricalHours = this.calculateAverageHistoricalHours(similarChanges);
      adjustedHours = (baseEffortHours * 0.6) + (avgHistoricalHours * 0.4);
    }

    // Apply resource type multipliers
    const resourceMultiplier = this.getResourceMultiplier(resourceTypes);
    adjustedHours *= resourceMultiplier;

    const minHours = Math.max(0, adjustedHours * 0.8);
    const maxHours = adjustedHours * 1.4;

    return {
      resourceTypes,
      estimatedHours: Math.round(adjustedHours),
      minHours: Math.round(minHours),
      maxHours: Math.round(maxHours),
      confidence: this.calculateResourceConfidence(similarChanges.length, resourceTypes.length),
      breakdown: this.calculateResourceBreakdown(resourceTypes, adjustedHours),
    };
  }

  /**
   * Assess risk impact using neural network models
   */
  private async assessRiskImpact(
    changeRequest: ChangeRequest,
    similarChanges: SimilarChange[],
  ): Promise<RiskImpactResult> {
    this.logger.debug('Assessing risk impact using neural network models');

    // Identify technical risks
    const technicalRisks = this.identifyTechnicalRisks(changeRequest);

    // Identify business risks
    const businessRisks = this.identifyBusinessRisks(changeRequest);

    // Calculate overall risk level
    const riskLevel = this.calculateOverallRiskLevel({
      technicalRisks,
      businessRisks,
      complexity: changeRequest.complexity,
      impactAreas: changeRequest.impactAreas,
      historicalRisks: this.extractHistoricalRisks(similarChanges),
    });

    // Generate mitigation recommendations
    const mitigationPlan = this.generateMitigationPlan(technicalRisks, businessRisks);

    return {
      category: riskLevel,
      technicalRisks,
      businessRisks,
      mitigationPlan,
      confidence: this.calculateRiskConfidence(similarChanges.length, technicalRisks.length + businessRisks.length),
      riskFactors: {
        complexityRisk: this.getComplexityRiskFactor(changeRequest.complexity),
        impactAreaRisk: this.getImpactAreaRiskFactor(changeRequest.impactAreas),
        historicalRiskPattern: this.analyzeHistoricalRiskPattern(similarChanges),
      },
    };
  }

  /**
   * Calculate overall impact using weighted algorithm
   */
  private calculateOverallImpact(params: {
    timeImpact: TimeImpactResult;
    costImpact: CostImpactResult;
    resourceImpact: ResourceImpactResult;
    riskImpact: RiskImpactResult;
    changeRequest: ChangeRequest;
  }): ImpactLevel {
    const { timeImpact, costImpact, resourceImpact, riskImpact } = params;

    // Convert individual impacts to numeric scores
    const timeScore = this.getImpactScore(timeImpact.category);
    const costScore = this.getImpactScore(costImpact.category);
    const resourceScore = this.getResourceImpactScore(resourceImpact.estimatedHours);
    const riskScore = this.getRiskImpactScore(riskImpact.category);

    // Apply weights
    const weightedScore = 
      (timeScore * 0.3) +
      (costScore * 0.3) +
      (resourceScore * 0.2) +
      (riskScore * 0.2);

    // Convert back to impact level
    return this.scoreToImpactLevel(weightedScore);
  }

  /**
   * Calculate confidence level based on multiple factors
   */
  private calculateConfidenceLevel(factors: {
    historicalDataQuality: number;
    similarityScore: number;
    dataCompleteness: number;
    algorithmCoverage: number;
  }): ConfidenceLevel {
    const { historicalDataQuality, similarityScore, dataCompleteness, algorithmCoverage } = factors;

    const overallConfidence = 
      (historicalDataQuality * 0.3) +
      (similarityScore * 0.3) +
      (dataCompleteness * 0.2) +
      (algorithmCoverage * 0.2);

    if (overallConfidence >= this.algorithmConfig.thresholds.highConfidence) {
      return ConfidenceLevel.VERY_HIGH;
    } else if (overallConfidence >= this.algorithmConfig.thresholds.mediumConfidence) {
      return ConfidenceLevel.HIGH;
    } else if (overallConfidence >= this.algorithmConfig.thresholds.lowConfidence) {
      return ConfidenceLevel.MEDIUM;
    } else {
      return ConfidenceLevel.LOW;
    }
  }

  // Helper methods for calculations (simplified implementations)
  private getBaseTimeEstimate(complexity: ChangeRequestComplexity): number {
    const estimates = {
      [ChangeRequestComplexity.TRIVIAL]: 1,
      [ChangeRequestComplexity.MINOR]: 3,
      [ChangeRequestComplexity.MODERATE]: 7,
      [ChangeRequestComplexity.MAJOR]: 14,
      [ChangeRequestComplexity.CRITICAL]: 30,
    };
    return estimates[complexity] || 7;
  }

  private getBaseCostEstimate(complexity: ChangeRequestComplexity): number {
    const estimates = {
      [ChangeRequestComplexity.TRIVIAL]: 500,
      [ChangeRequestComplexity.MINOR]: 2000,
      [ChangeRequestComplexity.MODERATE]: 8000,
      [ChangeRequestComplexity.MAJOR]: 25000,
      [ChangeRequestComplexity.CRITICAL]: 75000,
    };
    return estimates[complexity] || 8000;
  }

  private getBaseEffortHours(complexity: ChangeRequestComplexity): number {
    const estimates = {
      [ChangeRequestComplexity.TRIVIAL]: 8,
      [ChangeRequestComplexity.MINOR]: 24,
      [ChangeRequestComplexity.MODERATE]: 80,
      [ChangeRequestComplexity.MAJOR]: 200,
      [ChangeRequestComplexity.CRITICAL]: 500,
    };
    return estimates[complexity] || 80;
  }

  private identifyRequiredResources(impactAreas: ChangeRequestImpactArea[]): ResourceImpactType[] {
    const resourceMap: Record<ChangeRequestImpactArea, ResourceImpactType[]> = {
      [ChangeRequestImpactArea.FRONTEND]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.DESIGNER_TIME],
      [ChangeRequestImpactArea.BACKEND]: [ResourceImpactType.DEVELOPER_TIME],
      [ChangeRequestImpactArea.DATABASE]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.DEVOPS_TIME],
      [ChangeRequestImpactArea.API]: [ResourceImpactType.DEVELOPER_TIME],
      [ChangeRequestImpactArea.INTEGRATION]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.EXTERNAL_CONSULTANT],
      [ChangeRequestImpactArea.INFRASTRUCTURE]: [ResourceImpactType.DEVOPS_TIME, ResourceImpactType.INFRASTRUCTURE],
      [ChangeRequestImpactArea.SECURITY]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.EXTERNAL_CONSULTANT],
      [ChangeRequestImpactArea.PERFORMANCE]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.DEVOPS_TIME],
      [ChangeRequestImpactArea.USER_EXPERIENCE]: [ResourceImpactType.DESIGNER_TIME, ResourceImpactType.DEVELOPER_TIME],
      [ChangeRequestImpactArea.BUSINESS_LOGIC]: [ResourceImpactType.DEVELOPER_TIME],
      [ChangeRequestImpactArea.DOCUMENTATION]: [ResourceImpactType.DEVELOPER_TIME],
      [ChangeRequestImpactArea.TESTING]: [ResourceImpactType.QA_TIME],
      [ChangeRequestImpactArea.DEPLOYMENT]: [ResourceImpactType.DEVOPS_TIME],
      [ChangeRequestImpactArea.THIRD_PARTY]: [ResourceImpactType.EXTERNAL_CONSULTANT, ResourceImpactType.THIRD_PARTY_SERVICES],
      [ChangeRequestImpactArea.ALL_AREAS]: [ResourceImpactType.DEVELOPER_TIME, ResourceImpactType.QA_TIME, ResourceImpactType.DEVOPS_TIME],
    };

    const allResources = new Set<ResourceImpactType>();
    impactAreas.forEach(area => {
      resourceMap[area]?.forEach(resource => allResources.add(resource));
    });

    return Array.from(allResources);
  }

  private identifyTechnicalRisks(changeRequest: ChangeRequest): TechnicalRiskType[] {
    const risks: TechnicalRiskType[] = [];

    // Risk identification based on type and complexity
    if (changeRequest.complexity === ChangeRequestComplexity.CRITICAL) {
      risks.push(TechnicalRiskType.SYSTEM_STABILITY, TechnicalRiskType.TECHNICAL_DEBT);
    }

    if (changeRequest.impactAreas.includes(ChangeRequestImpactArea.INTEGRATION)) {
      risks.push(TechnicalRiskType.INTEGRATION_FAILURES);
    }

    if (changeRequest.impactAreas.includes(ChangeRequestImpactArea.DATABASE)) {
      risks.push(TechnicalRiskType.DATA_INTEGRITY);
    }

    if (changeRequest.impactAreas.includes(ChangeRequestImpactArea.SECURITY)) {
      risks.push(TechnicalRiskType.SECURITY_VULNERABILITIES);
    }

    return risks;
  }

  private identifyBusinessRisks(changeRequest: ChangeRequest): BusinessRiskType[] {
    const risks: BusinessRiskType[] = [];

    if (changeRequest.priority === ChangeRequestPriority.CRITICAL) {
      risks.push(BusinessRiskType.TIMELINE_DELAY, BusinessRiskType.BUDGET_OVERRUN);
    }

    if (changeRequest.type === ChangeRequestType.SCOPE_EXPANSION) {
      risks.push(BusinessRiskType.SCOPE_CREEP);
    }

    return risks;
  }

  // Additional helper methods would be implemented here...
  private calculateHistoricalDataQuality(changes: ChangeRequest[], assessments: ImpactAssessment[]): number {
    // Simplified quality calculation
    return Math.min(1, changes.length / 20) * (assessments.length / changes.length);
  }

  private calculateTimeRange(changes: ChangeRequest[]): { start: Date; end: Date } {
    const dates = changes.map(c => c.createdAt).sort();
    return {
      start: dates[0] || new Date(),
      end: dates[dates.length - 1] || new Date(),
    };
  }

  private assessDataCompleteness(changeRequest: ChangeRequest): number {
    let completeness = 0.5; // Base score
    
    if (changeRequest.businessJustification) completeness += 0.1;
    if (changeRequest.technicalDetails) completeness += 0.1;
    if (changeRequest.estimatedCost) completeness += 0.1;
    if (changeRequest.estimatedHours) completeness += 0.1;
    if (changeRequest.testingRequirements) completeness += 0.1;
    if (changeRequest.rollbackPlan) completeness += 0.1;

    return Math.min(1, completeness);
  }

  private assessAlgorithmCoverage(changeRequest: ChangeRequest): number {
    // Simplified coverage assessment
    return 0.8; // Assume good algorithm coverage
  }

  private assessQuality(params: {
    confidenceLevel: ConfidenceLevel;
    dataAvailability: boolean;
    algorithmComplexity: string;
  }): AssessmentQuality {
    const { confidenceLevel, dataAvailability, algorithmComplexity } = params;

    if (confidenceLevel === ConfidenceLevel.VERY_HIGH && dataAvailability && algorithmComplexity === 'high') {
      return AssessmentQuality.EXCELLENT;
    } else if (confidenceLevel === ConfidenceLevel.HIGH && dataAvailability) {
      return AssessmentQuality.GOOD;
    } else if (confidenceLevel === ConfidenceLevel.MEDIUM) {
      return AssessmentQuality.FAIR;
    } else {
      return AssessmentQuality.POOR;
    }
  }

  private generateAlgorithmRecommendations(params: {
    overallImpact: ImpactLevel;
    confidenceLevel: ConfidenceLevel;
    riskImpact: RiskImpactResult;
  }): string[] {
    const recommendations: string[] = [];

    if (params.overallImpact === ImpactLevel.SEVERE) {
      recommendations.push('Consider breaking down this change into smaller, manageable pieces');
    }

    if (params.confidenceLevel === ConfidenceLevel.LOW) {
      recommendations.push('Gather more detailed requirements to improve assessment accuracy');
    }

    if (params.riskImpact.category === RiskImpactCategory.CRITICAL_RISK) {
      recommendations.push('Implement comprehensive risk mitigation strategies before proceeding');
    }

    return recommendations;
  }

  private getDefaultRiskImpact(): RiskImpactResult {
    return {
      category: RiskImpactCategory.MEDIUM_RISK,
      technicalRisks: [],
      businessRisks: [],
      mitigationPlan: 'Standard risk mitigation procedures apply',
      confidence: 0.5,
      riskFactors: {
        complexityRisk: 0.5,
        impactAreaRisk: 0.5,
        historicalRiskPattern: 0.5,
      },
    };
  }

  // Additional helper methods for similarity calculations, impact scoring, etc.
  // These would be implemented based on specific business requirements
  private getComplexitySimilarity(current: ChangeRequestComplexity, historical: ChangeRequestComplexity): number {
    // Simplified similarity calculation
    return current === historical ? 1 : 0.5;
  }

  private getImpactAreaSimilarity(current: ChangeRequestImpactArea[], historical: ChangeRequestImpactArea[]): number {
    const intersection = current.filter(area => historical.includes(area));
    const union = [...new Set([...current, ...historical])];
    return intersection.length / union.length;
  }

  private getPrioritySimilarity(current: ChangeRequestPriority, historical: ChangeRequestPriority): number {
    return current === historical ? 1 : 0.7;
  }

  private identifyMatchingFactors(current: ChangeRequest, historical: ChangeRequest): string[] {
    const factors: string[] = [];
    
    if (current.type === historical.type) factors.push('type');
    if (current.complexity === historical.complexity) factors.push('complexity');
    if (current.priority === historical.priority) factors.push('priority');
    
    return factors;
  }

  private calculateAverageHistoricalTime(similarChanges: SimilarChange[]): number {
    const validChanges = similarChanges.filter(sc => sc.assessment?.estimatedDelayDays);
    if (validChanges.length === 0) return 0;

    const totalDays = validChanges.reduce((sum, sc) => sum + (sc.assessment?.estimatedDelayDays || 0), 0);
    return totalDays / validChanges.length;
  }

  private calculateAverageHistoricalCost(similarChanges: SimilarChange[]): number {
    const validChanges = similarChanges.filter(sc => sc.assessment?.estimatedAdditionalCost);
    if (validChanges.length === 0) return 0;

    const totalCost = validChanges.reduce((sum, sc) => sum + (sc.assessment?.estimatedAdditionalCost || 0), 0);
    return totalCost / validChanges.length;
  }

  private calculateAverageHistoricalHours(similarChanges: SimilarChange[]): number {
    const validChanges = similarChanges.filter(sc => sc.assessment?.estimatedEffortHours);
    if (validChanges.length === 0) return 0;

    const totalHours = validChanges.reduce((sum, sc) => sum + (sc.assessment?.estimatedEffortHours || 0), 0);
    return totalHours / validChanges.length;
  }

  private calculateImpactAreaMultiplier(impactAreas: ChangeRequestImpactArea[]): number {
    const multipliers: Record<ChangeRequestImpactArea, number> = {
      [ChangeRequestImpactArea.FRONTEND]: 1.0,
      [ChangeRequestImpactArea.BACKEND]: 1.1,
      [ChangeRequestImpactArea.DATABASE]: 1.3,
      [ChangeRequestImpactArea.API]: 1.1,
      [ChangeRequestImpactArea.INTEGRATION]: 1.4,
      [ChangeRequestImpactArea.INFRASTRUCTURE]: 1.5,
      [ChangeRequestImpactArea.SECURITY]: 1.3,
      [ChangeRequestImpactArea.PERFORMANCE]: 1.2,
      [ChangeRequestImpactArea.USER_EXPERIENCE]: 1.1,
      [ChangeRequestImpactArea.BUSINESS_LOGIC]: 1.2,
      [ChangeRequestImpactArea.DOCUMENTATION]: 0.8,
      [ChangeRequestImpactArea.TESTING]: 1.1,
      [ChangeRequestImpactArea.DEPLOYMENT]: 1.2,
      [ChangeRequestImpactArea.THIRD_PARTY]: 1.6,
      [ChangeRequestImpactArea.ALL_AREAS]: 2.0,
    };

    if (impactAreas.length === 0) return 1.0;

    const totalMultiplier = impactAreas.reduce((sum, area) => sum + (multipliers[area] || 1.0), 0);
    return totalMultiplier / impactAreas.length;
  }

  private getCostTypeMultiplier(type: ChangeRequestType): number {
    const multipliers: Record<ChangeRequestType, number> = {
      [ChangeRequestType.FEATURE_ADDITION]: 1.2,
      [ChangeRequestType.FEATURE_MODIFICATION]: 1.0,
      [ChangeRequestType.FEATURE_REMOVAL]: 0.8,
      [ChangeRequestType.TECHNICAL_CHANGE]: 1.3,
      [ChangeRequestType.DESIGN_ADJUSTMENT]: 0.9,
      [ChangeRequestType.SCOPE_EXPANSION]: 1.8,
      [ChangeRequestType.SCOPE_REDUCTION]: 0.6,
      [ChangeRequestType.INTEGRATION_CHANGE]: 1.5,
      [ChangeRequestType.PERFORMANCE_OPTIMIZATION]: 1.1,
      [ChangeRequestType.SECURITY_ENHANCEMENT]: 1.4,
      [ChangeRequestType.COMPLIANCE_REQUIREMENT]: 1.6,
      [ChangeRequestType.BUG_FIX]: 0.7,
      [ChangeRequestType.OTHER]: 1.0,
    };

    return multipliers[type] || 1.0;
  }

  private getResourceMultiplier(resourceTypes: ResourceImpactType[]): number {
    const multipliers: Record<ResourceImpactType, number> = {
      [ResourceImpactType.DEVELOPER_TIME]: 1.0,
      [ResourceImpactType.DESIGNER_TIME]: 0.8,
      [ResourceImpactType.PROJECT_MANAGER_TIME]: 0.3,
      [ResourceImpactType.QA_TIME]: 0.5,
      [ResourceImpactType.DEVOPS_TIME]: 0.7,
      [ResourceImpactType.EXTERNAL_CONSULTANT]: 1.5,
      [ResourceImpactType.THIRD_PARTY_SERVICES]: 1.2,
      [ResourceImpactType.INFRASTRUCTURE]: 1.1,
      [ResourceImpactType.TOOLS_AND_SOFTWARE]: 0.2,
      [ResourceImpactType.TRAINING]: 0.4,
    };

    if (resourceTypes.length === 0) return 1.0;

    const totalMultiplier = resourceTypes.reduce((sum, type) => sum + (multipliers[type] || 1.0), 0);
    return totalMultiplier / resourceTypes.length;
  }

  private categorizeTimeImpact(days: number): TimeImpactCategory {
    if (days === 0) return TimeImpactCategory.NO_IMPACT;
    if (days <= 2) return TimeImpactCategory.MINOR_DELAY;
    if (days <= 7) return TimeImpactCategory.MODERATE_DELAY;
    if (days <= 14) return TimeImpactCategory.SIGNIFICANT_DELAY;
    if (days <= 30) return TimeImpactCategory.MAJOR_DELAY;
    return TimeImpactCategory.CRITICAL_DELAY;
  }

  private categorizeCostImpact(cost: number): CostImpactCategory {
    if (cost === 0) return CostImpactCategory.NO_COST;
    if (cost <= 1000) return CostImpactCategory.MINIMAL_COST;
    if (cost <= 5000) return CostImpactCategory.LOW_COST;
    if (cost <= 20000) return CostImpactCategory.MODERATE_COST;
    if (cost <= 50000) return CostImpactCategory.HIGH_COST;
    return CostImpactCategory.VERY_HIGH_COST;
  }

  private calculateOverallRiskLevel(params: {
    technicalRisks: TechnicalRiskType[];
    businessRisks: BusinessRiskType[];
    complexity: ChangeRequestComplexity;
    impactAreas: ChangeRequestImpactArea[];
    historicalRisks: any[];
  }): RiskImpactCategory {
    const { technicalRisks, businessRisks, complexity, impactAreas } = params;

    let riskScore = 0;

    // Technical risk contribution
    riskScore += technicalRisks.length * 0.3;

    // Business risk contribution
    riskScore += businessRisks.length * 0.3;

    // Complexity contribution
    const complexityRisk = this.getComplexityRiskFactor(complexity);
    riskScore += complexityRisk * 0.2;

    // Impact area contribution
    const impactAreaRisk = this.getImpactAreaRiskFactor(impactAreas);
    riskScore += impactAreaRisk * 0.2;

    // Categorize risk level
    if (riskScore >= 3.0) return RiskImpactCategory.CRITICAL_RISK;
    if (riskScore >= 2.0) return RiskImpactCategory.HIGH_RISK;
    if (riskScore >= 1.0) return RiskImpactCategory.MEDIUM_RISK;
    if (riskScore >= 0.5) return RiskImpactCategory.LOW_RISK;
    return RiskImpactCategory.NO_RISK;
  }

  private getComplexityRiskFactor(complexity: ChangeRequestComplexity): number {
    const factors: Record<ChangeRequestComplexity, number> = {
      [ChangeRequestComplexity.TRIVIAL]: 0.1,
      [ChangeRequestComplexity.MINOR]: 0.3,
      [ChangeRequestComplexity.MODERATE]: 0.5,
      [ChangeRequestComplexity.MAJOR]: 0.8,
      [ChangeRequestComplexity.CRITICAL]: 1.0,
    };
    return factors[complexity] || 0.5;
  }

  private getImpactAreaRiskFactor(impactAreas: ChangeRequestImpactArea[]): number {
    const riskFactors: Record<ChangeRequestImpactArea, number> = {
      [ChangeRequestImpactArea.FRONTEND]: 0.2,
      [ChangeRequestImpactArea.BACKEND]: 0.4,
      [ChangeRequestImpactArea.DATABASE]: 0.8,
      [ChangeRequestImpactArea.API]: 0.3,
      [ChangeRequestImpactArea.INTEGRATION]: 0.9,
      [ChangeRequestImpactArea.INFRASTRUCTURE]: 1.0,
      [ChangeRequestImpactArea.SECURITY]: 0.9,
      [ChangeRequestImpactArea.PERFORMANCE]: 0.6,
      [ChangeRequestImpactArea.USER_EXPERIENCE]: 0.3,
      [ChangeRequestImpactArea.BUSINESS_LOGIC]: 0.5,
      [ChangeRequestImpactArea.DOCUMENTATION]: 0.1,
      [ChangeRequestImpactArea.TESTING]: 0.2,
      [ChangeRequestImpactArea.DEPLOYMENT]: 0.7,
      [ChangeRequestImpactArea.THIRD_PARTY]: 0.8,
      [ChangeRequestImpactArea.ALL_AREAS]: 1.0,
    };

    if (impactAreas.length === 0) return 0.5;

    const totalRisk = impactAreas.reduce((sum, area) => sum + (riskFactors[area] || 0.5), 0);
    return Math.min(1.0, totalRisk / impactAreas.length);
  }

  private generateMitigationPlan(technicalRisks: TechnicalRiskType[], businessRisks: BusinessRiskType[]): string {
    const mitigations: string[] = [];

    // Technical risk mitigations
    if (technicalRisks.includes(TechnicalRiskType.SYSTEM_STABILITY)) {
      mitigations.push('Implement comprehensive testing and staging environment validation');
    }
    if (technicalRisks.includes(TechnicalRiskType.INTEGRATION_FAILURES)) {
      mitigations.push('Create detailed integration test plans and fallback procedures');
    }
    if (technicalRisks.includes(TechnicalRiskType.DATA_INTEGRITY)) {
      mitigations.push('Implement data backup and validation procedures');
    }
    if (technicalRisks.includes(TechnicalRiskType.SECURITY_VULNERABILITIES)) {
      mitigations.push('Conduct security review and penetration testing');
    }

    // Business risk mitigations
    if (businessRisks.includes(BusinessRiskType.TIMELINE_DELAY)) {
      mitigations.push('Build buffer time into project schedule and identify critical path dependencies');
    }
    if (businessRisks.includes(BusinessRiskType.BUDGET_OVERRUN)) {
      mitigations.push('Implement strict budget monitoring and approval processes for scope changes');
    }
    if (businessRisks.includes(BusinessRiskType.SCOPE_CREEP)) {
      mitigations.push('Establish clear change control procedures and stakeholder sign-off requirements');
    }

    return mitigations.length > 0
      ? mitigations.join('; ')
      : 'Standard risk mitigation procedures apply';
  }

  private calculateResourceBreakdown(resourceTypes: ResourceImpactType[], totalHours: number): Record<ResourceImpactType, number> {
    const breakdown: Record<ResourceImpactType, number> = {};

    // Default distribution percentages
    const distributions: Record<ResourceImpactType, number> = {
      [ResourceImpactType.DEVELOPER_TIME]: 0.6,
      [ResourceImpactType.DESIGNER_TIME]: 0.15,
      [ResourceImpactType.PROJECT_MANAGER_TIME]: 0.1,
      [ResourceImpactType.QA_TIME]: 0.2,
      [ResourceImpactType.DEVOPS_TIME]: 0.1,
      [ResourceImpactType.EXTERNAL_CONSULTANT]: 0.3,
      [ResourceImpactType.THIRD_PARTY_SERVICES]: 0.05,
      [ResourceImpactType.INFRASTRUCTURE]: 0.05,
      [ResourceImpactType.TOOLS_AND_SOFTWARE]: 0.02,
      [ResourceImpactType.TRAINING]: 0.08,
    };

    resourceTypes.forEach(type => {
      breakdown[type] = Math.round(totalHours * (distributions[type] || 0.1));
    });

    return breakdown;
  }

  private extractHistoricalRisks(similarChanges: SimilarChange[]): any[] {
    return similarChanges.map(sc => ({
      technicalRisks: sc.assessment?.technicalRisks || [],
      businessRisks: sc.assessment?.businessRisks || [],
      riskLevel: sc.assessment?.riskImpactCategory,
    }));
  }

  private analyzeHistoricalRiskPattern(similarChanges: SimilarChange[]): number {
    if (similarChanges.length === 0) return 0.5;

    const riskLevels = similarChanges
      .map(sc => sc.assessment?.riskImpactCategory)
      .filter(level => level);

    if (riskLevels.length === 0) return 0.5;

    // Calculate average risk level
    const riskScores = riskLevels.map(level => this.getRiskLevelScore(level));
    const avgRiskScore = riskScores.reduce((sum, score) => sum + score, 0) / riskScores.length;

    return avgRiskScore;
  }

  private getRiskLevelScore(riskLevel: RiskImpactCategory): number {
    const scores: Record<RiskImpactCategory, number> = {
      [RiskImpactCategory.NO_RISK]: 0.0,
      [RiskImpactCategory.LOW_RISK]: 0.2,
      [RiskImpactCategory.MEDIUM_RISK]: 0.5,
      [RiskImpactCategory.HIGH_RISK]: 0.8,
      [RiskImpactCategory.CRITICAL_RISK]: 1.0,
    };
    return scores[riskLevel] || 0.5;
  }

  private getImpactScore(category: any): number {
    // Generic impact score mapping
    const scoreMap: Record<string, number> = {
      'no_impact': 0.0,
      'no_cost': 0.0,
      'minimal': 0.1,
      'minimal_cost': 0.1,
      'minor_delay': 0.2,
      'low': 0.2,
      'low_cost': 0.2,
      'moderate_delay': 0.4,
      'moderate': 0.4,
      'moderate_cost': 0.4,
      'medium': 0.5,
      'significant_delay': 0.6,
      'high': 0.7,
      'high_cost': 0.7,
      'major_delay': 0.8,
      'very_high_cost': 0.9,
      'critical_delay': 1.0,
      'severe': 1.0,
    };

    return scoreMap[category] || 0.5;
  }

  private getResourceImpactScore(hours: number): number {
    if (hours <= 8) return 0.1;
    if (hours <= 40) return 0.3;
    if (hours <= 160) return 0.5;
    if (hours <= 400) return 0.7;
    return 0.9;
  }

  private getRiskImpactScore(category: RiskImpactCategory): number {
    return this.getRiskLevelScore(category);
  }

  private scoreToImpactLevel(score: number): ImpactLevel {
    if (score >= 0.9) return ImpactLevel.SEVERE;
    if (score >= 0.7) return ImpactLevel.HIGH;
    if (score >= 0.4) return ImpactLevel.MEDIUM;
    if (score >= 0.2) return ImpactLevel.LOW;
    return ImpactLevel.MINIMAL;
  }

  private calculateTimeConfidence(similarChangesCount: number, multiplier: number): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence with more similar changes
    confidence += Math.min(0.3, similarChangesCount * 0.05);

    // Adjust for multiplier complexity
    if (multiplier > 1.5) confidence -= 0.1;
    if (multiplier < 0.8) confidence += 0.1;

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  private calculateCostConfidence(similarChangesCount: number, multiplier: number): number {
    return this.calculateTimeConfidence(similarChangesCount, multiplier);
  }

  private calculateResourceConfidence(similarChangesCount: number, resourceTypesCount: number): number {
    let confidence = 0.5;

    confidence += Math.min(0.3, similarChangesCount * 0.05);
    confidence += Math.min(0.2, resourceTypesCount * 0.03);

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  private calculateRiskConfidence(similarChangesCount: number, totalRisks: number): number {
    let confidence = 0.6; // Higher base for risk assessment

    confidence += Math.min(0.2, similarChangesCount * 0.03);

    // More identified risks can increase confidence in risk assessment
    confidence += Math.min(0.2, totalRisks * 0.02);

    return Math.max(0.1, Math.min(1.0, confidence));
  }
}

// Type definitions for algorithm results
interface HistoricalData {
  changes: ChangeRequest[];
  assessments: ImpactAssessment[];
  dataPoints: number;
  quality: number;
  timeRange: { start: Date; end: Date };
}

interface SimilarChange {
  changeRequest: ChangeRequest;
  assessment?: ImpactAssessment;
  similarity: number;
  matchingFactors: string[];
}

interface TimeImpactResult {
  category: TimeImpactCategory;
  estimatedDays: number;
  minDays: number;
  maxDays: number;
  confidence: number;
  factors: {
    baseEstimate: number;
    historicalAdjustment: number | null;
    impactAreaMultiplier: number;
    similarChangesCount: number;
  };
}

interface CostImpactResult {
  category: CostImpactCategory;
  estimatedCost: number;
  minCost: number;
  maxCost: number;
  confidence: number;
  factors: {
    baseEstimate: number;
    historicalAdjustment: number | null;
    typeMultiplier: number;
    similarChangesCount: number;
  };
}

interface ResourceImpactResult {
  resourceTypes: ResourceImpactType[];
  estimatedHours: number;
  minHours: number;
  maxHours: number;
  confidence: number;
  breakdown: Record<ResourceImpactType, number>;
}

interface RiskImpactResult {
  category: RiskImpactCategory;
  technicalRisks: TechnicalRiskType[];
  businessRisks: BusinessRiskType[];
  mitigationPlan: string;
  confidence: number;
  riskFactors: {
    complexityRisk: number;
    impactAreaRisk: number;
    historicalRiskPattern: number;
  };
}

interface AlgorithmMetadata {
  version: string;
  executionTime: number;
  modelsUsed: string[];
  historicalDataPoints: number;
  similarChangesFound: number;
  confidenceFactors: {
    historicalData: number;
    similarity: number;
    dataCompleteness: number;
  };
  recommendations: string[];
}
