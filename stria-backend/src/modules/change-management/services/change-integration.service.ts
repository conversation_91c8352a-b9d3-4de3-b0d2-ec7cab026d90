import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { ChangeRequest } from '../entities/change-request.entity';
import { ApprovalWorkflow } from '../entities/approval-workflow.entity';
import { ImpactAssessment } from '../entities/impact-assessment.entity';
import { Project } from '../../projects/entities/project.entity';
import {
  IntegrationType,
  IntegrationStatus,
  SyncDirection,
  WebhookEventType,
  ExternalSystemType,
  DataMappingType,
} from '../enums';

/**
 * Change Integration Service
 * Sprint 7: Change Request Management System
 * 
 * Handles integration with external systems including Jira, GitHub, Slack,
 * project management tools, and custom webhooks
 */
@Injectable()
export class ChangeIntegrationService {
  private readonly logger = new Logger(ChangeIntegrationService.name);

  // Integration configuration
  private readonly integrationConfig = {
    jira: {
      enabled: false,
      baseUrl: '',
      username: '',
      apiToken: '',
      projectKey: '',
      issueTypeMapping: {
        'feature_addition': 'Story',
        'bug_fix': 'Bug',
        'technical_change': 'Task',
        'scope_expansion': 'Epic',
      },
      statusMapping: {
        'submitted': 'To Do',
        'under_review': 'In Progress',
        'pending_approval': 'In Review',
        'approved': 'Done',
        'rejected': 'Rejected',
        'completed': 'Closed',
      },
    },
    github: {
      enabled: false,
      token: '',
      organization: '',
      repository: '',
      labelMapping: {
        'high': 'priority:high',
        'critical': 'priority:critical',
        'feature_addition': 'type:feature',
        'bug_fix': 'type:bug',
      },
    },
    slack: {
      enabled: false,
      botToken: '',
      channels: {
        notifications: '#change-requests',
        approvals: '#approvals',
        alerts: '#alerts',
      },
    },
    webhooks: {
      enabled: true,
      endpoints: [],
      retryAttempts: 3,
      timeoutMs: 5000,
    },
  };

  constructor(
    @InjectRepository(ChangeRequest)
    private readonly changeRequestRepository: Repository<ChangeRequest>,
    @InjectRepository(ApprovalWorkflow)
    private readonly workflowRepository: Repository<ApprovalWorkflow>,
    @InjectRepository(ImpactAssessment)
    private readonly assessmentRepository: Repository<ImpactAssessment>,
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.loadIntegrationConfig();
  }

  /**
   * Sync change request with external systems
   */
  async syncChangeRequest(
    changeRequest: ChangeRequest,
    systems: ExternalSystemType[],
    direction: SyncDirection = SyncDirection.OUTBOUND,
  ): Promise<IntegrationResult> {
    this.logger.log(`Syncing change request ${changeRequest.id} with systems: ${systems.join(', ')}`);

    const results: SystemSyncResult[] = [];

    for (const system of systems) {
      try {
        const syncResult = await this.syncWithSystem(system, changeRequest, direction);
        results.push(syncResult);
      } catch (error) {
        this.logger.error(`Sync failed for system ${system}: ${error.message}`);
        results.push({
          system,
          success: false,
          error: error.message,
          timestamp: new Date(),
        });
      }
    }

    return {
      changeRequestId: changeRequest.id,
      direction,
      systemResults: results,
      overallSuccess: results.every(r => r.success),
      syncedAt: new Date(),
    };
  }

  /**
   * Create Jira issue from change request
   */
  async createJiraIssue(changeRequest: ChangeRequest): Promise<JiraIntegrationResult> {
    if (!this.integrationConfig.jira.enabled) {
      throw new Error('Jira integration is not enabled');
    }

    this.logger.log(`Creating Jira issue for change request: ${changeRequest.id}`);

    try {
      const issueData = this.mapChangeRequestToJiraIssue(changeRequest);
      
      const response = await this.httpService.post(
        `${this.integrationConfig.jira.baseUrl}/rest/api/3/issue`,
        issueData,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(
              `${this.integrationConfig.jira.username}:${this.integrationConfig.jira.apiToken}`
            ).toString('base64')}`,
            'Content-Type': 'application/json',
          },
        }
      ).toPromise();

      const jiraIssue = response?.data;

      // Update change request with Jira issue key
      await this.changeRequestRepository.update(changeRequest.id, {
        externalReferences: {
          ...changeRequest.externalReferences,
          jira: {
            issueKey: jiraIssue.key,
            issueId: jiraIssue.id,
            url: `${this.integrationConfig.jira.baseUrl}/browse/${jiraIssue.key}`,
          },
        },
      });

      return {
        success: true,
        issueKey: jiraIssue.key,
        issueId: jiraIssue.id,
        url: `${this.integrationConfig.jira.baseUrl}/browse/${jiraIssue.key}`,
      };

    } catch (error) {
      this.logger.error(`Jira issue creation failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update Jira issue status
   */
  async updateJiraIssueStatus(
    changeRequest: ChangeRequest,
    newStatus: string,
  ): Promise<JiraIntegrationResult> {
    if (!this.integrationConfig.jira.enabled) {
      return { success: false, error: 'Jira integration is not enabled' };
    }

    const jiraRef = changeRequest.externalReferences?.jira;
    if (!jiraRef?.issueKey) {
      return { success: false, error: 'No Jira issue reference found' };
    }

    try {
      const jiraStatus = this.integrationConfig.jira.statusMapping[newStatus] || newStatus;
      
      // Get available transitions
      const transitionsResponse = await this.httpService.get(
        `${this.integrationConfig.jira.baseUrl}/rest/api/3/issue/${jiraRef.issueKey}/transitions`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(
              `${this.integrationConfig.jira.username}:${this.integrationConfig.jira.apiToken}`
            ).toString('base64')}`,
          },
        }
      ).toPromise();

      const transitions = transitionsResponse?.data.transitions || [];
      const targetTransition = transitions.find((t: any) => t.to.name === jiraStatus);

      if (!targetTransition) {
        return { 
          success: false, 
          error: `No transition available to status: ${jiraStatus}` 
        };
      }

      // Execute transition
      await this.httpService.post(
        `${this.integrationConfig.jira.baseUrl}/rest/api/3/issue/${jiraRef.issueKey}/transitions`,
        {
          transition: {
            id: targetTransition.id,
          },
        },
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(
              `${this.integrationConfig.jira.username}:${this.integrationConfig.jira.apiToken}`
            ).toString('base64')}`,
            'Content-Type': 'application/json',
          },
        }
      ).toPromise();

      return {
        success: true,
        issueKey: jiraRef.issueKey,
        updatedStatus: jiraStatus,
      };

    } catch (error) {
      this.logger.error(`Jira status update failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create GitHub issue from change request
   */
  async createGitHubIssue(changeRequest: ChangeRequest): Promise<GitHubIntegrationResult> {
    if (!this.integrationConfig.github.enabled) {
      throw new Error('GitHub integration is not enabled');
    }

    this.logger.log(`Creating GitHub issue for change request: ${changeRequest.id}`);

    try {
      const issueData = this.mapChangeRequestToGitHubIssue(changeRequest);
      
      const response = await this.httpService.post(
        `https://api.github.com/repos/${this.integrationConfig.github.organization}/${this.integrationConfig.github.repository}/issues`,
        issueData,
        {
          headers: {
            'Authorization': `token ${this.integrationConfig.github.token}`,
            'Content-Type': 'application/json',
          },
        }
      ).toPromise();

      const githubIssue = response?.data;

      // Update change request with GitHub issue reference
      await this.changeRequestRepository.update(changeRequest.id, {
        externalReferences: {
          ...changeRequest.externalReferences,
          github: {
            issueNumber: githubIssue.number,
            issueId: githubIssue.id,
            url: githubIssue.html_url,
          },
        },
      });

      return {
        success: true,
        issueNumber: githubIssue.number,
        issueId: githubIssue.id,
        url: githubIssue.html_url,
      };

    } catch (error) {
      this.logger.error(`GitHub issue creation failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send Slack notification
   */
  async sendSlackNotification(
    channel: string,
    message: SlackMessage,
  ): Promise<SlackIntegrationResult> {
    if (!this.integrationConfig.slack.enabled) {
      return { success: false, error: 'Slack integration is not enabled' };
    }

    try {
      const response = await this.httpService.post(
        'https://slack.com/api/chat.postMessage',
        {
          channel,
          ...message,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.integrationConfig.slack.botToken}`,
            'Content-Type': 'application/json',
          },
        }
      ).toPromise();

      const slackResponse = response?.data;

      if (!slackResponse.ok) {
        throw new Error(slackResponse.error || 'Slack API error');
      }

      return {
        success: true,
        messageTs: slackResponse.ts,
        channel: slackResponse.channel,
      };

    } catch (error) {
      this.logger.error(`Slack notification failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Send webhook notification
   */
  async sendWebhook(
    url: string,
    eventType: WebhookEventType,
    payload: Record<string, any>,
  ): Promise<WebhookResult> {
    this.logger.log(`Sending webhook: ${eventType} to ${url}`);

    const webhookPayload = {
      event: eventType,
      timestamp: new Date().toISOString(),
      data: payload,
    };

    let attempt = 0;
    const maxAttempts = this.integrationConfig.webhooks.retryAttempts;

    while (attempt < maxAttempts) {
      try {
        const response = await this.httpService.post(url, webhookPayload, {
          timeout: this.integrationConfig.webhooks.timeoutMs,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Stria-ChangeManagement/1.0',
          },
        }).toPromise();

        return {
          success: true,
          statusCode: response?.status || 200,
          attempt: attempt + 1,
          url,
        };

      } catch (error) {
        attempt++;
        this.logger.warn(`Webhook attempt ${attempt} failed: ${error.message}`);

        if (attempt >= maxAttempts) {
          return {
            success: false,
            error: error.message,
            attempt,
            url,
          };
        }

        // Wait before retry (exponential backoff)
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }

    return {
      success: false,
      error: 'Max retry attempts exceeded',
      attempt,
      url,
    };
  }

  /**
   * Handle incoming webhook from external systems
   */
  async handleIncomingWebhook(
    source: ExternalSystemType,
    payload: Record<string, any>,
  ): Promise<WebhookHandleResult> {
    this.logger.log(`Handling incoming webhook from: ${source}`);

    try {
      switch (source) {
        case ExternalSystemType.JIRA:
          return await this.handleJiraWebhook(payload);
        
        case ExternalSystemType.GITHUB:
          return await this.handleGitHubWebhook(payload);
        
        case ExternalSystemType.SLACK:
          return await this.handleSlackWebhook(payload);
        
        default:
          return await this.handleGenericWebhook(source, payload);
      }
    } catch (error) {
      this.logger.error(`Webhook handling failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        source,
        error: error.message,
      };
    }
  }

  /**
   * Export change requests to external format
   */
  async exportChangeRequests(
    format: 'csv' | 'json' | 'xml',
    filters?: Record<string, any>,
  ): Promise<ExportResult> {
    this.logger.log(`Exporting change requests in ${format} format`);

    try {
      // Get change requests based on filters
      const queryBuilder = this.changeRequestRepository.createQueryBuilder('cr');
      
      if (filters) {
        // Apply filters
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryBuilder.andWhere(`cr.${key} = :${key}`, { [key]: value });
          }
        });
      }

      const changeRequests = await queryBuilder.getMany();

      // Convert to requested format
      let exportData: string;
      let mimeType: string;

      switch (format) {
        case 'csv':
          exportData = this.convertToCSV(changeRequests);
          mimeType = 'text/csv';
          break;
        
        case 'json':
          exportData = JSON.stringify(changeRequests, null, 2);
          mimeType = 'application/json';
          break;
        
        case 'xml':
          exportData = this.convertToXML(changeRequests);
          mimeType = 'application/xml';
          break;
        
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      return {
        success: true,
        format,
        data: exportData,
        mimeType,
        recordCount: changeRequests.length,
      };

    } catch (error) {
      this.logger.error(`Export failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        format,
        error: error.message,
        recordCount: 0,
      };
    }
  }

  /**
   * Private helper methods
   */
  private loadIntegrationConfig(): void {
    // Load configuration from environment variables or config service
    this.integrationConfig.jira.enabled = this.configService.get<boolean>('JIRA_ENABLED', false);
    this.integrationConfig.jira.baseUrl = this.configService.get<string>('JIRA_BASE_URL', '');
    this.integrationConfig.jira.username = this.configService.get<string>('JIRA_USERNAME', '');
    this.integrationConfig.jira.apiToken = this.configService.get<string>('JIRA_API_TOKEN', '');

    this.integrationConfig.github.enabled = this.configService.get<boolean>('GITHUB_ENABLED', false);
    this.integrationConfig.github.token = this.configService.get<string>('GITHUB_TOKEN', '');
    this.integrationConfig.github.organization = this.configService.get<string>('GITHUB_ORG', '');
    this.integrationConfig.github.repository = this.configService.get<string>('GITHUB_REPO', '');

    this.integrationConfig.slack.enabled = this.configService.get<boolean>('SLACK_ENABLED', false);
    this.integrationConfig.slack.botToken = this.configService.get<string>('SLACK_BOT_TOKEN', '');
  }

  private async syncWithSystem(
    system: ExternalSystemType,
    changeRequest: ChangeRequest,
    direction: SyncDirection,
  ): Promise<SystemSyncResult> {
    switch (system) {
      case ExternalSystemType.JIRA:
        return await this.syncWithJira(changeRequest, direction);
      
      case ExternalSystemType.GITHUB:
        return await this.syncWithGitHub(changeRequest, direction);
      
      case ExternalSystemType.SLACK:
        return await this.syncWithSlack(changeRequest, direction);
      
      default:
        throw new Error(`Unsupported system: ${system}`);
    }
  }

  private async syncWithJira(
    changeRequest: ChangeRequest,
    direction: SyncDirection,
  ): Promise<SystemSyncResult> {
    if (direction === SyncDirection.OUTBOUND) {
      const result = await this.createJiraIssue(changeRequest);
      return {
        system: ExternalSystemType.JIRA,
        success: result.success,
        data: result,
        error: result.error,
        timestamp: new Date(),
      };
    }
    
    // TODO: Implement inbound sync
    return {
      system: ExternalSystemType.JIRA,
      success: false,
      error: 'Inbound sync not implemented',
      timestamp: new Date(),
    };
  }

  private async syncWithGitHub(
    changeRequest: ChangeRequest,
    direction: SyncDirection,
  ): Promise<SystemSyncResult> {
    if (direction === SyncDirection.OUTBOUND) {
      const result = await this.createGitHubIssue(changeRequest);
      return {
        system: ExternalSystemType.GITHUB,
        success: result.success,
        data: result,
        error: result.error,
        timestamp: new Date(),
      };
    }
    
    // TODO: Implement inbound sync
    return {
      system: ExternalSystemType.GITHUB,
      success: false,
      error: 'Inbound sync not implemented',
      timestamp: new Date(),
    };
  }

  private async syncWithSlack(
    changeRequest: ChangeRequest,
    direction: SyncDirection,
  ): Promise<SystemSyncResult> {
    // TODO: Implement Slack sync
    return {
      system: ExternalSystemType.SLACK,
      success: false,
      error: 'Slack sync not implemented',
      timestamp: new Date(),
    };
  }

  private mapChangeRequestToJiraIssue(changeRequest: ChangeRequest): any {
    const issueType = this.integrationConfig.jira.issueTypeMapping[changeRequest.type] || 'Task';
    
    return {
      fields: {
        project: {
          key: this.integrationConfig.jira.projectKey,
        },
        summary: changeRequest.title,
        description: changeRequest.description,
        issuetype: {
          name: issueType,
        },
        priority: {
          name: this.mapPriorityToJira(changeRequest.priority),
        },
        labels: [
          `stria-change-${changeRequest.id}`,
          `type-${changeRequest.type}`,
          `complexity-${changeRequest.complexity}`,
        ],
      },
    };
  }

  private mapChangeRequestToGitHubIssue(changeRequest: ChangeRequest): any {
    const labels = [
      `stria-change-${changeRequest.id}`,
      this.integrationConfig.github.labelMapping[changeRequest.priority] || 'priority:medium',
      this.integrationConfig.github.labelMapping[changeRequest.type] || 'type:change',
    ];

    return {
      title: changeRequest.title,
      body: this.formatGitHubIssueBody(changeRequest),
      labels,
    };
  }

  private formatGitHubIssueBody(changeRequest: ChangeRequest): string {
    return `
## Change Request Details

**Type:** ${changeRequest.type}
**Priority:** ${changeRequest.priority}
**Complexity:** ${changeRequest.complexity}

## Description
${changeRequest.description}

## Business Justification
${changeRequest.businessJustification || 'Not provided'}

## Technical Details
${changeRequest.technicalDetails || 'Not provided'}

---
*This issue was automatically created from Stria Change Request #${changeRequest.id}*
    `.trim();
  }

  private mapPriorityToJira(priority: string): string {
    const mapping: Record<string, string> = {
      'low': 'Low',
      'medium': 'Medium',
      'high': 'High',
      'urgent': 'Highest',
      'critical': 'Highest',
    };
    return mapping[priority] || 'Medium';
  }

  private async handleJiraWebhook(payload: any): Promise<WebhookHandleResult> {
    // TODO: Implement Jira webhook handling
    return {
      success: true,
      source: ExternalSystemType.JIRA,
      processed: true,
    };
  }

  private async handleGitHubWebhook(payload: any): Promise<WebhookHandleResult> {
    // TODO: Implement GitHub webhook handling
    return {
      success: true,
      source: ExternalSystemType.GITHUB,
      processed: true,
    };
  }

  private async handleSlackWebhook(payload: any): Promise<WebhookHandleResult> {
    // TODO: Implement Slack webhook handling
    return {
      success: true,
      source: ExternalSystemType.SLACK,
      processed: true,
    };
  }

  private async handleGenericWebhook(
    source: ExternalSystemType,
    payload: any,
  ): Promise<WebhookHandleResult> {
    // TODO: Implement generic webhook handling
    return {
      success: true,
      source,
      processed: true,
    };
  }

  private convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  private convertToXML(data: any[]): string {
    // Simple XML conversion - would need more sophisticated implementation
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<changeRequests>\n';
    
    for (const item of data) {
      xml += '  <changeRequest>\n';
      for (const [key, value] of Object.entries(item)) {
        xml += `    <${key}>${value}</${key}>\n`;
      }
      xml += '  </changeRequest>\n';
    }
    
    xml += '</changeRequests>';
    return xml;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Type definitions
interface IntegrationResult {
  changeRequestId: string;
  direction: SyncDirection;
  systemResults: SystemSyncResult[];
  overallSuccess: boolean;
  syncedAt: Date;
}

interface SystemSyncResult {
  system: ExternalSystemType;
  success: boolean;
  data?: any;
  error?: string;
  timestamp: Date;
}

interface JiraIntegrationResult {
  success: boolean;
  issueKey?: string;
  issueId?: string;
  url?: string;
  updatedStatus?: string;
  error?: string;
}

interface GitHubIntegrationResult {
  success: boolean;
  issueNumber?: number;
  issueId?: number;
  url?: string;
  error?: string;
}

interface SlackIntegrationResult {
  success: boolean;
  messageTs?: string;
  channel?: string;
  error?: string;
}

interface SlackMessage {
  text?: string;
  blocks?: any[];
  attachments?: any[];
}

interface WebhookResult {
  success: boolean;
  statusCode?: number;
  attempt: number;
  url: string;
  error?: string;
}

interface WebhookHandleResult {
  success: boolean;
  source: ExternalSystemType;
  processed?: boolean;
  error?: string;
}

interface ExportResult {
  success: boolean;
  format: string;
  data?: string;
  mimeType?: string;
  recordCount: number;
  error?: string;
}
