import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException, ConflictException, BadRequestException } from '@nestjs/common';
import { ChangeRequestService } from '../change-request.service';
import { ChangeRequest } from '../../entities/change-request.entity';
import { ImpactAssessment } from '../../entities/impact-assessment.entity';
import { ApprovalWorkflow } from '../../entities/approval-workflow.entity';
import { Project } from '../../../projects/entities/project.entity';
import { User } from '../../../users/entities/user.entity';
import {
  ChangeRequestStatus,
  ChangeRequestType,
  ChangeRequestPriority,
  ChangeRequestComplexity,
  ChangeRequestImpactArea,
  ChangeRequestSource,
} from '../../enums';

/**
 * Change Request Service Unit Tests
 * Sprint 7: Change Request Management System
 * 
 * Comprehensive unit tests for the ChangeRequestService
 */
describe('ChangeRequestService', () => {
  let service: ChangeRequestService;
  let changeRequestRepository: Repository<ChangeRequest>;
  let impactAssessmentRepository: Repository<ImpactAssessment>;
  let workflowRepository: Repository<ApprovalWorkflow>;
  let projectRepository: Repository<Project>;
  let userRepository: Repository<User>;

  // Mock data
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
  };

  const mockProject = {
    id: 'project-123',
    name: 'Test Project',
    clientId: 'user-123',
  };

  const mockChangeRequest = {
    id: 'change-123',
    title: 'Test Change Request',
    description: 'Test description',
    type: ChangeRequestType.FEATURE_ADDITION,
    priority: ChangeRequestPriority.MEDIUM,
    complexity: ChangeRequestComplexity.MODERATE,
    status: ChangeRequestStatus.DRAFT,
    projectId: 'project-123',
    requesterId: 'user-123',
    impactAreas: [ChangeRequestImpactArea.FRONTEND],
    businessJustification: 'Business need',
    technicalDetails: 'Technical details',
    estimatedCost: 5000,
    estimatedHours: 40,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Mock repositories
  const mockChangeRequestRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
  };

  const mockImpactAssessmentRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockWorkflowRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockProjectRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChangeRequestService,
        {
          provide: getRepositoryToken(ChangeRequest),
          useValue: mockChangeRequestRepository,
        },
        {
          provide: getRepositoryToken(ImpactAssessment),
          useValue: mockImpactAssessmentRepository,
        },
        {
          provide: getRepositoryToken(ApprovalWorkflow),
          useValue: mockWorkflowRepository,
        },
        {
          provide: getRepositoryToken(Project),
          useValue: mockProjectRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<ChangeRequestService>(ChangeRequestService);
    changeRequestRepository = module.get<Repository<ChangeRequest>>(
      getRepositoryToken(ChangeRequest),
    );
    impactAssessmentRepository = module.get<Repository<ImpactAssessment>>(
      getRepositoryToken(ImpactAssessment),
    );
    workflowRepository = module.get<Repository<ApprovalWorkflow>>(
      getRepositoryToken(ApprovalWorkflow),
    );
    projectRepository = module.get<Repository<Project>>(
      getRepositoryToken(Project),
    );
    userRepository = module.get<Repository<User>>(
      getRepositoryToken(User),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createDto = {
      title: 'New Change Request',
      description: 'New description',
      type: ChangeRequestType.FEATURE_ADDITION,
      priority: ChangeRequestPriority.HIGH,
      complexity: ChangeRequestComplexity.MAJOR,
      source: ChangeRequestSource.CLIENT_REQUEST,
      projectId: 'project-123',
      impactAreas: [ChangeRequestImpactArea.FRONTEND, ChangeRequestImpactArea.BACKEND],
      businessJustification: 'Business need',
      technicalDetails: 'Technical details',
      estimatedCost: 10000,
      estimatedHours: 80,
    };

    it('should create a change request successfully', async () => {
      // Arrange
      mockProjectRepository.findOne.mockResolvedValue(mockProject);
      mockChangeRequestRepository.create.mockReturnValue(mockChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue(mockChangeRequest);

      // Act
      const result = await service.create(createDto, mockUser.id);

      // Assert
      expect(mockProjectRepository.findOne).toHaveBeenCalledWith({
        where: { id: createDto.projectId },
        relations: ['client'],
      });
      expect(mockChangeRequestRepository.create).toHaveBeenCalledWith({
        ...createDto,
        requesterId: mockUser.id,
        status: ChangeRequestStatus.DRAFT,
      });
      expect(mockChangeRequestRepository.save).toHaveBeenCalledWith(mockChangeRequest);
      expect(result).toBeDefined();
      expect(result.title).toBe(createDto.title);
    });

    it('should throw NotFoundException when project does not exist', async () => {
      // Arrange
      mockProjectRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createDto, mockUser.id)).rejects.toThrow(
        NotFoundException,
      );
      expect(mockProjectRepository.findOne).toHaveBeenCalledWith({
        where: { id: createDto.projectId },
        relations: ['client'],
      });
    });

    it('should throw ForbiddenException when user has no access to project', async () => {
      // Arrange
      const projectWithDifferentClient = {
        ...mockProject,
        clientId: 'different-user',
      };
      mockProjectRepository.findOne.mockResolvedValue(projectWithDifferentClient);

      // Act & Assert
      await expect(service.create(createDto, mockUser.id)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('findById', () => {
    it('should return change request when found and user has access', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: mockProject,
      });

      // Act
      const result = await service.findById(mockChangeRequest.id, mockUser.id);

      // Assert
      expect(mockChangeRequestRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockChangeRequest.id },
        relations: ['project', 'requester', 'assignedTo', 'approvedBy'],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockChangeRequest.id);
    });

    it('should throw NotFoundException when change request does not exist', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById('non-existent', mockUser.id)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException when user has no access', async () => {
      // Arrange
      const changeRequestWithDifferentUser = {
        ...mockChangeRequest,
        requesterId: 'different-user',
        assignedToId: 'another-user',
        project: { ...mockProject, clientId: 'different-client' },
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(changeRequestWithDifferentUser);

      // Act & Assert
      await expect(service.findById(mockChangeRequest.id, mockUser.id)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('update', () => {
    const updateDto = {
      title: 'Updated Title',
      description: 'Updated description',
      priority: ChangeRequestPriority.HIGH,
      version: 1,
    };

    it('should update change request successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        ...updateDto,
        version: 2,
      });

      // Act
      const result = await service.update(mockChangeRequest.id, updateDto, mockUser.id);

      // Assert
      expect(mockChangeRequestRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockChangeRequest.id },
        relations: ['project'],
      });
      expect(mockChangeRequestRepository.save).toHaveBeenCalled();
      expect(result.title).toBe(updateDto.title);
      expect(result.version).toBe(2);
    });

    it('should throw ConflictException on version mismatch', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        version: 2, // Different version
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);

      // Act & Assert
      await expect(
        service.update(mockChangeRequest.id, updateDto, mockUser.id),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw ForbiddenException when trying to update non-draft status', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.SUBMITTED,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);

      // Act & Assert
      await expect(
        service.update(mockChangeRequest.id, updateDto, mockUser.id),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('updateStatus', () => {
    const statusDto = {
      status: ChangeRequestStatus.SUBMITTED,
      reason: 'Ready for review',
    };

    it('should update status successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        status: statusDto.status,
        submittedAt: new Date(),
      });

      // Act
      const result = await service.updateStatus(
        mockChangeRequest.id,
        statusDto,
        mockUser.id,
      );

      // Assert
      expect(result.status).toBe(statusDto.status);
      expect(result.submittedAt).toBeDefined();
    });

    it('should throw ForbiddenException for invalid status transition', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.COMPLETED,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);

      // Act & Assert
      await expect(
        service.updateStatus(mockChangeRequest.id, statusDto, mockUser.id),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('assign', () => {
    const assignDto = {
      assignedToId: 'assignee-123',
      reason: 'Best fit for this task',
    };

    it('should assign change request successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.SUBMITTED,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockUserRepository.findOne.mockResolvedValue({ id: assignDto.assignedToId });
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        assignedToId: assignDto.assignedToId,
        status: ChangeRequestStatus.UNDER_REVIEW,
      });

      // Act
      const result = await service.assign(mockChangeRequest.id, assignDto, mockUser.id);

      // Assert
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: assignDto.assignedToId },
      });
      expect(result.assignedToId).toBe(assignDto.assignedToId);
      expect(result.status).toBe(ChangeRequestStatus.UNDER_REVIEW);
    });

    it('should throw NotFoundException when assignee does not exist', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.SUBMITTED,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockUserRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.assign(mockChangeRequest.id, assignDto, mockUser.id),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('approve', () => {
    const approveDto = {
      approvalComments: 'Looks good to proceed',
      conditions: ['Complete testing before deployment'],
      approvedBudget: 8000,
    };

    it('should approve change request successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.PENDING_APPROVAL,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        status: ChangeRequestStatus.APPROVED,
        approvedById: mockUser.id,
        approvedAt: new Date(),
      });

      // Act
      const result = await service.approve(mockChangeRequest.id, approveDto, mockUser.id);

      // Assert
      expect(result.status).toBe(ChangeRequestStatus.APPROVED);
      expect(result.approvedById).toBe(mockUser.id);
      expect(result.approvedAt).toBeDefined();
    });

    it('should throw ForbiddenException when not in pending approval status', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.DRAFT,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);

      // Act & Assert
      await expect(
        service.approve(mockChangeRequest.id, approveDto, mockUser.id),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('reject', () => {
    const rejectDto = {
      rejectionReason: 'Insufficient business justification',
      feedback: 'Please provide more details',
    };

    it('should reject change request successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.PENDING_APPROVAL,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.save.mockResolvedValue({
        ...existingChangeRequest,
        status: ChangeRequestStatus.REJECTED,
        rejectionReason: rejectDto.rejectionReason,
        rejectedAt: new Date(),
      });

      // Act
      const result = await service.reject(mockChangeRequest.id, rejectDto, mockUser.id);

      // Assert
      expect(result.status).toBe(ChangeRequestStatus.REJECTED);
      expect(result.rejectionReason).toBe(rejectDto.rejectionReason);
      expect(result.rejectedAt).toBeDefined();
    });
  });

  describe('delete', () => {
    it('should delete draft change request successfully', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.DRAFT,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);
      mockChangeRequestRepository.delete.mockResolvedValue({ affected: 1 });

      // Act
      await service.delete(mockChangeRequest.id, mockUser.id);

      // Assert
      expect(mockChangeRequestRepository.delete).toHaveBeenCalledWith(mockChangeRequest.id);
    });

    it('should throw ForbiddenException when trying to delete non-draft request', async () => {
      // Arrange
      const existingChangeRequest = {
        ...mockChangeRequest,
        status: ChangeRequestStatus.SUBMITTED,
        project: mockProject,
      };
      mockChangeRequestRepository.findOne.mockResolvedValue(existingChangeRequest);

      // Act & Assert
      await expect(service.delete(mockChangeRequest.id, mockUser.id)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated change requests', async () => {
      // Arrange
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockChangeRequest], 1]),
      };

      mockChangeRequestRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.findAll(mockUser.id, {
        page: 1,
        limit: 20,
        status: ChangeRequestStatus.DRAFT,
      });

      // Assert
      expect(result.items).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
    });
  });

  describe('getStatistics', () => {
    it('should return change request statistics', async () => {
      // Arrange
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockChangeRequest]),
        getRawMany: jest.fn().mockResolvedValue([
          { status: 'draft', count: '5' },
          { status: 'submitted', count: '3' },
        ]),
      };

      mockChangeRequestRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Act
      const result = await service.getStatistics(mockUser.id, {});

      // Assert
      expect(result).toBeDefined();
      expect(result.totalChangeRequests).toBeDefined();
      expect(result.byStatus).toBeDefined();
    });
  });
});
