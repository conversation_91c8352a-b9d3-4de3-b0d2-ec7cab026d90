import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { ImpactAssessmentService } from '../impact-assessment.service';
import { AssessmentAlgorithmEngineService } from '../assessment-algorithm-engine.service';
import { ImpactAssessment } from '../../entities/impact-assessment.entity';
import { ChangeRequest } from '../../entities/change-request.entity';
import { User } from '../../../users/entities/user.entity';
import {
  ImpactAssessmentStatus,
  ImpactAssessmentMethod,
  ImpactLevel,
  ConfidenceLevel,
  AssessmentQuality,
  TimeImpactCategory,
  CostImpactCategory,
  RiskImpactCategory,
  ChangeRequestType,
  ChangeRequestComplexity,
} from '../../enums';

/**
 * Impact Assessment Service Unit Tests
 * Sprint 7: Change Request Management System
 * 
 * Comprehensive unit tests for the ImpactAssessmentService
 */
describe('ImpactAssessmentService', () => {
  let service: ImpactAssessmentService;
  let assessmentRepository: Repository<ImpactAssessment>;
  let changeRequestRepository: Repository<ChangeRequest>;
  let userRepository: Repository<User>;
  let algorithmEngine: AssessmentAlgorithmEngineService;

  // Mock data
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
  };

  const mockChangeRequest = {
    id: 'change-123',
    title: 'Test Change Request',
    description: 'Test description',
    type: ChangeRequestType.FEATURE_ADDITION,
    complexity: ChangeRequestComplexity.MODERATE,
    projectId: 'project-123',
    requesterId: 'user-123',
    businessJustification: 'Business need',
    technicalDetails: 'Technical details',
    estimatedCost: 5000,
    estimatedHours: 40,
  };

  const mockAssessment = {
    id: 'assessment-123',
    changeRequestId: 'change-123',
    status: ImpactAssessmentStatus.COMPLETED,
    method: ImpactAssessmentMethod.AUTOMATED,
    overallImpact: ImpactLevel.MEDIUM,
    confidenceLevel: ConfidenceLevel.HIGH,
    assessmentQuality: AssessmentQuality.GOOD,
    timeImpactCategory: TimeImpactCategory.MODERATE_DELAY,
    estimatedDelayDays: 5,
    minDelayDays: 3,
    maxDelayDays: 7,
    costImpactCategory: CostImpactCategory.MODERATE_COST,
    estimatedAdditionalCost: 2000,
    minAdditionalCost: 1500,
    maxAdditionalCost: 2500,
    riskImpactCategory: RiskImpactCategory.MEDIUM_RISK,
    assessmentSummary: 'Moderate impact assessment',
    assessedById: 'user-123',
    assessmentStartedAt: new Date(),
    assessmentCompletedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockAlgorithmResult = {
    overallImpact: ImpactLevel.MEDIUM,
    confidenceLevel: ConfidenceLevel.HIGH,
    assessmentQuality: AssessmentQuality.GOOD,
    timeImpact: {
      category: TimeImpactCategory.MODERATE_DELAY,
      estimatedDays: 5,
      minDays: 3,
      maxDays: 7,
      confidence: 0.8,
      factors: {},
    },
    costImpact: {
      category: CostImpactCategory.MODERATE_COST,
      estimatedCost: 2000,
      minCost: 1500,
      maxCost: 2500,
      confidence: 0.8,
      factors: {},
    },
    resourceImpact: {
      resourceTypes: [],
      estimatedHours: 40,
      minHours: 30,
      maxHours: 50,
      confidence: 0.8,
      breakdown: {},
    },
    riskImpact: {
      category: RiskImpactCategory.MEDIUM_RISK,
      technicalRisks: [],
      businessRisks: [],
      mitigationPlan: 'Standard mitigation',
      confidence: 0.8,
      riskFactors: {},
    },
    algorithmMetadata: {
      version: '1.0.0',
      executionTime: 1000,
      modelsUsed: [],
      historicalDataPoints: 10,
      similarChangesFound: 5,
      confidenceFactors: {},
      recommendations: [],
    },
  };

  // Mock repositories
  const mockAssessmentRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockChangeRequestRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockAlgorithmEngine = {
    runAssessmentAlgorithm: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImpactAssessmentService,
        {
          provide: getRepositoryToken(ImpactAssessment),
          useValue: mockAssessmentRepository,
        },
        {
          provide: getRepositoryToken(ChangeRequest),
          useValue: mockChangeRequestRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: AssessmentAlgorithmEngineService,
          useValue: mockAlgorithmEngine,
        },
      ],
    }).compile();

    service = module.get<ImpactAssessmentService>(ImpactAssessmentService);
    assessmentRepository = module.get<Repository<ImpactAssessment>>(
      getRepositoryToken(ImpactAssessment),
    );
    changeRequestRepository = module.get<Repository<ChangeRequest>>(
      getRepositoryToken(ChangeRequest),
    );
    userRepository = module.get<Repository<User>>(
      getRepositoryToken(User),
    );
    algorithmEngine = module.get<AssessmentAlgorithmEngineService>(
      AssessmentAlgorithmEngineService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createDto = {
      changeRequestId: 'change-123',
      method: ImpactAssessmentMethod.MANUAL,
      assessmentSummary: 'Manual assessment',
      overallImpact: ImpactLevel.MEDIUM,
      timeImpactCategory: TimeImpactCategory.MODERATE_DELAY,
      estimatedDelayDays: 5,
      costImpactCategory: CostImpactCategory.MODERATE_COST,
      estimatedAdditionalCost: 2000,
      riskImpactCategory: RiskImpactCategory.MEDIUM_RISK,
    };

    it('should create impact assessment successfully', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: mockUser.id },
      });
      mockAssessmentRepository.findOne.mockResolvedValue(null); // No existing assessment
      mockAssessmentRepository.create.mockReturnValue(mockAssessment);
      mockAssessmentRepository.save.mockResolvedValue(mockAssessment);

      // Act
      const result = await service.create(createDto, mockUser.id);

      // Assert
      expect(mockChangeRequestRepository.findOne).toHaveBeenCalledWith({
        where: { id: createDto.changeRequestId },
        relations: ['project'],
      });
      expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({
        where: { changeRequestId: createDto.changeRequestId },
      });
      expect(mockAssessmentRepository.create).toHaveBeenCalled();
      expect(mockAssessmentRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.changeRequestId).toBe(createDto.changeRequestId);
    });

    it('should throw NotFoundException when change request does not exist', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createDto, mockUser.id)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ConflictException when assessment already exists', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: mockUser.id },
      });
      mockAssessmentRepository.findOne.mockResolvedValue(mockAssessment);

      // Act & Assert
      await expect(service.create(createDto, mockUser.id)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('createAutomatedAssessment', () => {
    const requestDto = {
      changeRequestId: 'change-123',
      useHistoricalData: true,
      useMachineLearning: true,
      includeRiskAnalysis: true,
      confidenceThreshold: 0.7,
    };

    it('should create automated assessment successfully', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: mockUser.id },
      });
      mockAssessmentRepository.findOne.mockResolvedValue(null);
      mockAlgorithmEngine.runAssessmentAlgorithm.mockResolvedValue(mockAlgorithmResult);
      mockAssessmentRepository.create.mockReturnValue(mockAssessment);
      mockAssessmentRepository.save.mockResolvedValue(mockAssessment);

      // Act
      const result = await service.createAutomatedAssessment(requestDto, mockUser.id);

      // Assert
      expect(mockAlgorithmEngine.runAssessmentAlgorithm).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'change-123' }),
        {
          useHistoricalData: requestDto.useHistoricalData,
          useMachineLearning: requestDto.useMachineLearning,
          includeRiskAnalysis: requestDto.includeRiskAnalysis,
          confidenceThreshold: requestDto.confidenceThreshold,
        },
      );
      expect(mockAssessmentRepository.create).toHaveBeenCalled();
      expect(mockAssessmentRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.method).toBe(ImpactAssessmentMethod.AUTOMATED);
    });

    it('should handle algorithm engine errors gracefully', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: mockUser.id },
      });
      mockAssessmentRepository.findOne.mockResolvedValue(null);
      mockAlgorithmEngine.runAssessmentAlgorithm.mockRejectedValue(
        new Error('Algorithm failed'),
      );

      // Act & Assert
      await expect(
        service.createAutomatedAssessment(requestDto, mockUser.id),
      ).rejects.toThrow('Algorithm failed');
    });
  });

  describe('createManualAssessment', () => {
    const inputDto = {
      changeRequestId: 'change-123',
      assessmentSummary: 'Expert manual assessment',
      overallImpact: ImpactLevel.HIGH,
      confidenceLevel: ConfidenceLevel.VERY_HIGH,
      timeImpactCategory: TimeImpactCategory.SIGNIFICANT_DELAY,
      estimatedDelayDays: 10,
      minDelayDays: 8,
      maxDelayDays: 12,
      costImpactCategory: CostImpactCategory.HIGH_COST,
      estimatedAdditionalCost: 5000,
      minAdditionalCost: 4000,
      maxAdditionalCost: 6000,
      riskImpactCategory: RiskImpactCategory.HIGH_RISK,
      technicalRisks: ['integration_complexity', 'data_migration'],
      businessRisks: ['timeline_delay', 'budget_overrun'],
      riskMitigationPlan: 'Detailed mitigation plan',
      expertNotes: 'Expert analysis notes',
      reviewRequired: true,
    };

    it('should create manual assessment successfully', async () => {
      // Arrange
      mockChangeRequestRepository.findOne.mockResolvedValue({
        ...mockChangeRequest,
        project: { clientId: mockUser.id },
      });
      mockAssessmentRepository.findOne.mockResolvedValue(null);
      mockAssessmentRepository.create.mockReturnValue({
        ...mockAssessment,
        method: ImpactAssessmentMethod.EXPERT_MANUAL,
      });
      mockAssessmentRepository.save.mockResolvedValue({
        ...mockAssessment,
        method: ImpactAssessmentMethod.EXPERT_MANUAL,
      });

      // Act
      const result = await service.createManualAssessment(inputDto, mockUser.id);

      // Assert
      expect(mockAssessmentRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          changeRequestId: inputDto.changeRequestId,
          method: ImpactAssessmentMethod.EXPERT_MANUAL,
          overallImpact: inputDto.overallImpact,
          confidenceLevel: inputDto.confidenceLevel,
          assessedById: mockUser.id,
        }),
      );
      expect(result.method).toBe(ImpactAssessmentMethod.EXPERT_MANUAL);
    });
  });

  describe('findById', () => {
    it('should return assessment when found and user has access', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue({
        ...mockAssessment,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      });

      // Act
      const result = await service.findById(mockAssessment.id, mockUser.id);

      // Assert
      expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockAssessment.id },
        relations: ['changeRequest', 'changeRequest.project'],
      });
      expect(result).toBeDefined();
      expect(result.id).toBe(mockAssessment.id);
    });

    it('should throw NotFoundException when assessment does not exist', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById('non-existent', mockUser.id)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findByChangeRequestId', () => {
    it('should return assessment for change request', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue({
        ...mockAssessment,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      });

      // Act
      const result = await service.findByChangeRequestId('change-123', mockUser.id);

      // Assert
      expect(mockAssessmentRepository.findOne).toHaveBeenCalledWith({
        where: { changeRequestId: 'change-123' },
        relations: ['changeRequest', 'changeRequest.project'],
      });
      expect(result).toBeDefined();
      expect(result.changeRequestId).toBe('change-123');
    });

    it('should throw NotFoundException when no assessment exists for change request', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.findByChangeRequestId('change-123', mockUser.id),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('completeAssessment', () => {
    it('should complete assessment successfully', async () => {
      // Arrange
      const incompleteAssessment = {
        ...mockAssessment,
        status: ImpactAssessmentStatus.IN_PROGRESS,
        assessmentCompletedAt: null,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      };
      mockAssessmentRepository.findOne.mockResolvedValue(incompleteAssessment);
      mockAssessmentRepository.save.mockResolvedValue({
        ...incompleteAssessment,
        status: ImpactAssessmentStatus.COMPLETED,
        assessmentCompletedAt: new Date(),
      });

      // Act
      const result = await service.completeAssessment(mockAssessment.id, mockUser.id);

      // Assert
      expect(mockAssessmentRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: ImpactAssessmentStatus.COMPLETED,
          assessmentCompletedAt: expect.any(Date),
        }),
      );
      expect(result.status).toBe(ImpactAssessmentStatus.COMPLETED);
    });

    it('should throw error when assessment is already completed', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue({
        ...mockAssessment,
        status: ImpactAssessmentStatus.COMPLETED,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      });

      // Act & Assert
      await expect(
        service.completeAssessment(mockAssessment.id, mockUser.id),
      ).rejects.toThrow('Assessment is already completed');
    });
  });

  describe('validateAssessmentData', () => {
    it('should validate assessment data successfully', async () => {
      // Arrange
      const validAssessment = {
        ...mockAssessment,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      };
      mockAssessmentRepository.findOne.mockResolvedValue(validAssessment);

      // Act
      const result = await service.validateAssessmentData(mockAssessment.id, mockUser.id);

      // Assert
      expect(result).toBeDefined();
      expect(result.isValid).toBe(true);
      expect(result.validationErrors).toHaveLength(0);
    });

    it('should identify validation errors in assessment data', async () => {
      // Arrange
      const invalidAssessment = {
        ...mockAssessment,
        estimatedDelayDays: null, // Missing required field
        estimatedAdditionalCost: -100, // Invalid negative value
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      };
      mockAssessmentRepository.findOne.mockResolvedValue(invalidAssessment);

      // Act
      const result = await service.validateAssessmentData(mockAssessment.id, mockUser.id);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.validationErrors.length).toBeGreaterThan(0);
    });
  });

  describe('calculateAssessmentQuality', () => {
    it('should calculate assessment quality correctly', () => {
      // Arrange
      const assessment = {
        ...mockAssessment,
        confidenceLevel: ConfidenceLevel.VERY_HIGH,
        method: ImpactAssessmentMethod.EXPERT_MANUAL,
        assessmentSummary: 'Detailed assessment with comprehensive analysis',
      };

      // Act
      const quality = service.calculateAssessmentQuality(assessment);

      // Assert
      expect(quality).toBe(AssessmentQuality.EXCELLENT);
    });

    it('should return poor quality for incomplete assessments', () => {
      // Arrange
      const assessment = {
        ...mockAssessment,
        confidenceLevel: ConfidenceLevel.LOW,
        method: ImpactAssessmentMethod.AUTOMATED,
        assessmentSummary: null,
      };

      // Act
      const quality = service.calculateAssessmentQuality(assessment);

      // Assert
      expect(quality).toBe(AssessmentQuality.POOR);
    });
  });

  describe('generateAssessmentReport', () => {
    it('should generate comprehensive assessment report', async () => {
      // Arrange
      mockAssessmentRepository.findOne.mockResolvedValue({
        ...mockAssessment,
        changeRequest: {
          ...mockChangeRequest,
          project: { clientId: mockUser.id },
        },
      });

      // Act
      const report = await service.generateAssessmentReport(mockAssessment.id, mockUser.id);

      // Assert
      expect(report).toBeDefined();
      expect(report.assessmentId).toBe(mockAssessment.id);
      expect(report.summary).toBeDefined();
      expect(report.recommendations).toBeDefined();
      expect(report.riskAnalysis).toBeDefined();
      expect(report.impactBreakdown).toBeDefined();
    });
  });
});
