import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { ChangeRequest } from '../entities/change-request.entity';
import { ApprovalWorkflow, ApprovalStep } from '../entities/approval-workflow.entity';
import { ImpactAssessment } from '../entities/impact-assessment.entity';
import { User } from '../../users/entities/user.entity';
import { ChangeNotificationService } from './change-notification.service';
import { AssessmentAlgorithmEngineService } from './assessment-algorithm-engine.service';
import {
  ChangeRequestStatus,
  WorkflowStatus,
  ApprovalStepStatus,
  ApprovalDecision,
  AutomationTrigger,
  AutomationAction,
  AutomationCondition,
  WorkflowType,
  ImpactLevel,
  ConfidenceLevel,
} from '../enums';

/**
 * Workflow Automation Service
 * Sprint 7: Change Request Management System
 * 
 * Handles automated workflow processes, triggers, and business rules
 * Provides intelligent automation for change management workflows
 */
@Injectable()
export class WorkflowAutomationService {
  private readonly logger = new Logger(WorkflowAutomationService.name);

  // Automation configuration
  private readonly automationConfig = {
    rules: {
      autoApproval: {
        enabled: true,
        conditions: {
          maxCost: 1000,
          maxHours: 8,
          allowedTypes: ['bug_fix', 'design_adjustment'],
          requiredConfidence: 0.8,
        },
      },
      autoEscalation: {
        enabled: true,
        thresholds: {
          overdueDays: 2,
          criticalPriorityHours: 4,
          highPriorityHours: 24,
        },
      },
      autoAssessment: {
        enabled: true,
        triggers: ['change_request_created', 'change_request_updated'],
        conditions: {
          minComplexity: 'minor',
          requiredFields: ['businessJustification', 'technicalDetails'],
        },
      },
      autoNotification: {
        enabled: true,
        intervals: {
          reminderHours: 24,
          escalationHours: 48,
          finalWarningHours: 72,
        },
      },
    },
    scheduling: {
      processAutomations: '*/5 * * * *', // Every 5 minutes
      sendReminders: '0 9,17 * * *', // 9 AM and 5 PM daily
      cleanupExpired: '0 2 * * *', // 2 AM daily
      generateReports: '0 8 * * 1', // 8 AM every Monday
    },
  };

  constructor(
    @InjectRepository(ChangeRequest)
    private readonly changeRequestRepository: Repository<ChangeRequest>,
    @InjectRepository(ApprovalWorkflow)
    private readonly workflowRepository: Repository<ApprovalWorkflow>,
    @InjectRepository(ApprovalStep)
    private readonly stepRepository: Repository<ApprovalStep>,
    @InjectRepository(ImpactAssessment)
    private readonly assessmentRepository: Repository<ImpactAssessment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly notificationService: ChangeNotificationService,
    private readonly algorithmEngine: AssessmentAlgorithmEngineService,
  ) {}

  /**
   * Process automation triggers for a change request
   */
  async processAutomationTriggers(
    trigger: AutomationTrigger,
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<AutomationResult> {
    this.logger.log(`Processing automation trigger: ${trigger} for change request: ${changeRequest.id}`);

    const startTime = Date.now();
    const actions: AutomationActionResult[] = [];

    try {
      // Get applicable automation rules
      const rules = await this.getApplicableRules(trigger, changeRequest);

      // Process each rule
      for (const rule of rules) {
        if (await this.evaluateRuleConditions(rule, changeRequest, context)) {
          const actionResult = await this.executeAutomationAction(rule.action, changeRequest, context);
          actions.push(actionResult);
        }
      }

      const endTime = Date.now();

      return {
        success: true,
        trigger,
        changeRequestId: changeRequest.id,
        actionsExecuted: actions.length,
        actions,
        executionTime: endTime - startTime,
      };

    } catch (error) {
      this.logger.error(`Automation processing failed: ${error.message}`, error.stack);
      
      return {
        success: false,
        trigger,
        changeRequestId: changeRequest.id,
        actionsExecuted: actions.length,
        actions,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  /**
   * Auto-approve eligible change requests
   */
  async processAutoApprovals(): Promise<AutomationResult[]> {
    this.logger.log('Processing auto-approval candidates');

    if (!this.automationConfig.rules.autoApproval.enabled) {
      return [];
    }

    const candidates = await this.findAutoApprovalCandidates();
    const results: AutomationResult[] = [];

    for (const changeRequest of candidates) {
      try {
        const assessment = await this.assessmentRepository.findOne({
          where: { changeRequestId: changeRequest.id },
        });

        if (await this.isEligibleForAutoApproval(changeRequest, assessment)) {
          const result = await this.executeAutoApproval(changeRequest, assessment);
          results.push(result);
        }
      } catch (error) {
        this.logger.error(`Auto-approval failed for ${changeRequest.id}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Process workflow escalations
   */
  async processEscalations(): Promise<AutomationResult[]> {
    this.logger.log('Processing workflow escalations');

    if (!this.automationConfig.rules.autoEscalation.enabled) {
      return [];
    }

    const overdueWorkflows = await this.findOverdueWorkflows();
    const results: AutomationResult[] = [];

    for (const workflow of overdueWorkflows) {
      try {
        const escalationResult = await this.executeEscalation(workflow);
        results.push(escalationResult);
      } catch (error) {
        this.logger.error(`Escalation failed for workflow ${workflow.id}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Trigger automatic impact assessments
   */
  async triggerAutoAssessments(): Promise<AutomationResult[]> {
    this.logger.log('Triggering automatic impact assessments');

    if (!this.automationConfig.rules.autoAssessment.enabled) {
      return [];
    }

    const candidates = await this.findAssessmentCandidates();
    const results: AutomationResult[] = [];

    for (const changeRequest of candidates) {
      try {
        const assessmentResult = await this.executeAutoAssessment(changeRequest);
        results.push(assessmentResult);
      } catch (error) {
        this.logger.error(`Auto-assessment failed for ${changeRequest.id}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Scheduled automation processing
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async processScheduledAutomations(): Promise<void> {
    this.logger.debug('Running scheduled automation processing');

    try {
      // Process auto-approvals
      await this.processAutoApprovals();

      // Process escalations
      await this.processEscalations();

      // Trigger auto-assessments
      await this.triggerAutoAssessments();

      // Process workflow timeouts
      await this.processWorkflowTimeouts();

    } catch (error) {
      this.logger.error(`Scheduled automation processing failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Send automated reminders
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async sendAutomatedReminders(): Promise<void> {
    this.logger.log('Sending automated reminders');

    try {
      await this.notificationService.sendReminders();
    } catch (error) {
      this.logger.error(`Automated reminders failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Cleanup expired workflows and data
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredData(): Promise<void> {
    this.logger.log('Cleaning up expired automation data');

    try {
      // Cleanup expired workflows
      await this.cleanupExpiredWorkflows();

      // Cleanup old automation logs
      await this.cleanupAutomationLogs();

    } catch (error) {
      this.logger.error(`Data cleanup failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Private helper methods
   */
  private async getApplicableRules(
    trigger: AutomationTrigger,
    changeRequest: ChangeRequest,
  ): Promise<AutomationRule[]> {
    // This would typically come from a database or configuration
    const rules: AutomationRule[] = [];

    // Auto-assessment rule
    if (trigger === AutomationTrigger.CHANGE_REQUEST_CREATED) {
      rules.push({
        id: 'auto-assessment-on-create',
        trigger,
        conditions: [
          {
            field: 'complexity',
            operator: 'gte',
            value: 'minor',
          },
          {
            field: 'businessJustification',
            operator: 'exists',
            value: true,
          },
        ],
        action: AutomationAction.TRIGGER_IMPACT_ASSESSMENT,
        priority: 1,
        enabled: true,
      });
    }

    // Auto-approval rule
    if (trigger === AutomationTrigger.IMPACT_ASSESSMENT_COMPLETED) {
      rules.push({
        id: 'auto-approval-low-impact',
        trigger,
        conditions: [
          {
            field: 'overallImpact',
            operator: 'in',
            value: ['minimal', 'low'],
          },
          {
            field: 'confidenceLevel',
            operator: 'gte',
            value: 'high',
          },
          {
            field: 'estimatedCost',
            operator: 'lte',
            value: 1000,
          },
        ],
        action: AutomationAction.AUTO_APPROVE,
        priority: 2,
        enabled: this.automationConfig.rules.autoApproval.enabled,
      });
    }

    return rules.filter(rule => rule.enabled);
  }

  private async evaluateRuleConditions(
    rule: AutomationRule,
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<boolean> {
    for (const condition of rule.conditions) {
      if (!await this.evaluateCondition(condition, changeRequest, context)) {
        return false;
      }
    }
    return true;
  }

  private async evaluateCondition(
    condition: AutomationRuleCondition,
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<boolean> {
    const fieldValue = await this.getFieldValue(condition.field, changeRequest, context);

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'not_equals':
        return fieldValue !== condition.value;
      case 'gt':
        return fieldValue > condition.value;
      case 'gte':
        return fieldValue >= condition.value;
      case 'lt':
        return fieldValue < condition.value;
      case 'lte':
        return fieldValue <= condition.value;
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue);
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
      case 'exists':
        return condition.value ? (fieldValue !== null && fieldValue !== undefined) : (fieldValue === null || fieldValue === undefined);
      case 'contains':
        return typeof fieldValue === 'string' && fieldValue.includes(condition.value);
      default:
        return false;
    }
  }

  private async getFieldValue(
    field: string,
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<any> {
    // Handle direct change request fields
    if (field in changeRequest) {
      return changeRequest[field as keyof ChangeRequest];
    }

    // Handle assessment fields
    if (field.startsWith('assessment.')) {
      const assessmentField = field.replace('assessment.', '');
      const assessment = context?.assessment || await this.assessmentRepository.findOne({
        where: { changeRequestId: changeRequest.id },
      });
      return assessment ? assessment[assessmentField as keyof ImpactAssessment] : null;
    }

    // Handle workflow fields
    if (field.startsWith('workflow.')) {
      const workflowField = field.replace('workflow.', '');
      const workflow = context?.workflow || await this.workflowRepository.findOne({
        where: { changeRequestId: changeRequest.id },
      });
      return workflow ? workflow[workflowField as keyof ApprovalWorkflow] : null;
    }

    return null;
  }

  private async executeAutomationAction(
    action: AutomationAction,
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<AutomationActionResult> {
    const startTime = Date.now();

    try {
      switch (action) {
        case AutomationAction.TRIGGER_IMPACT_ASSESSMENT:
          return await this.executeTriggerAssessment(changeRequest);
        
        case AutomationAction.AUTO_APPROVE:
          return await this.executeAutoApprovalAction(changeRequest, context);
        
        case AutomationAction.ESCALATE_WORKFLOW:
          return await this.executeEscalationAction(changeRequest, context);
        
        case AutomationAction.SEND_NOTIFICATION:
          return await this.executeSendNotification(changeRequest, context);
        
        case AutomationAction.UPDATE_STATUS:
          return await this.executeUpdateStatus(changeRequest, context);
        
        case AutomationAction.ASSIGN_USER:
          return await this.executeAssignUser(changeRequest, context);
        
        default:
          throw new Error(`Unsupported automation action: ${action}`);
      }
    } catch (error) {
      return {
        action,
        success: false,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async executeTriggerAssessment(changeRequest: ChangeRequest): Promise<AutomationActionResult> {
    const startTime = Date.now();

    try {
      // Check if assessment already exists
      const existingAssessment = await this.assessmentRepository.findOne({
        where: { changeRequestId: changeRequest.id },
      });

      if (existingAssessment) {
        return {
          action: AutomationAction.TRIGGER_IMPACT_ASSESSMENT,
          success: false,
          executionTime: Date.now() - startTime,
          error: 'Assessment already exists',
        };
      }

      // Trigger automated assessment
      const assessmentResult = await this.algorithmEngine.runAssessmentAlgorithm(changeRequest, {
        useHistoricalData: true,
        useMachineLearning: true,
        includeRiskAnalysis: true,
      });

      // Create assessment record
      const assessment = this.assessmentRepository.create({
        changeRequestId: changeRequest.id,
        status: 'completed',
        method: 'automated',
        overallImpact: assessmentResult.overallImpact,
        confidenceLevel: assessmentResult.confidenceLevel,
        assessmentQuality: assessmentResult.assessmentQuality,
        timeImpactCategory: assessmentResult.timeImpact.category,
        estimatedDelayDays: assessmentResult.timeImpact.estimatedDays,
        minDelayDays: assessmentResult.timeImpact.minDays,
        maxDelayDays: assessmentResult.timeImpact.maxDays,
        costImpactCategory: assessmentResult.costImpact.category,
        estimatedAdditionalCost: assessmentResult.costImpact.estimatedCost,
        minAdditionalCost: assessmentResult.costImpact.minCost,
        maxAdditionalCost: assessmentResult.costImpact.maxCost,
        resourceImpactTypes: assessmentResult.resourceImpact.resourceTypes,
        estimatedEffortHours: assessmentResult.resourceImpact.estimatedHours,
        minEffortHours: assessmentResult.resourceImpact.minHours,
        maxEffortHours: assessmentResult.resourceImpact.maxHours,
        riskImpactCategory: assessmentResult.riskImpact.category,
        technicalRisks: assessmentResult.riskImpact.technicalRisks,
        businessRisks: assessmentResult.riskImpact.businessRisks,
        riskMitigationPlan: assessmentResult.riskImpact.mitigationPlan,
        assessmentSummary: 'Automated assessment completed',
        assessmentData: assessmentResult.algorithmMetadata,
        assessmentStartedAt: new Date(),
        assessmentCompletedAt: new Date(),
      });

      await this.assessmentRepository.save(assessment);

      // Send notification
      await this.notificationService.notifyImpactAssessmentCompleted(
        changeRequest,
        assessment.id,
        assessmentResult.overallImpact,
        assessmentResult.confidenceLevel === ConfidenceLevel.VERY_LOW,
      );

      return {
        action: AutomationAction.TRIGGER_IMPACT_ASSESSMENT,
        success: true,
        executionTime: Date.now() - startTime,
        data: { assessmentId: assessment.id },
      };

    } catch (error) {
      return {
        action: AutomationAction.TRIGGER_IMPACT_ASSESSMENT,
        success: false,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  private async executeAutoApprovalAction(
    changeRequest: ChangeRequest,
    context?: AutomationContext,
  ): Promise<AutomationActionResult> {
    const startTime = Date.now();

    try {
      // Update change request status
      await this.changeRequestRepository.update(changeRequest.id, {
        status: ChangeRequestStatus.APPROVED,
        approvedAt: new Date(),
        approvedById: 'system', // System approval
      });

      // Send notification
      await this.notificationService.notifyChangeRequestStatusUpdate(
        changeRequest,
        changeRequest.status,
        ChangeRequestStatus.APPROVED,
        'system',
        'Automatically approved based on low impact assessment',
      );

      return {
        action: AutomationAction.AUTO_APPROVE,
        success: true,
        executionTime: Date.now() - startTime,
        data: { approvedBy: 'system' },
      };

    } catch (error) {
      return {
        action: AutomationAction.AUTO_APPROVE,
        success: false,
        executionTime: Date.now() - startTime,
        error: error.message,
      };
    }
  }

  // Additional automation action implementations would go here...
  private async executeEscalationAction(changeRequest: ChangeRequest, context?: AutomationContext): Promise<AutomationActionResult> {
    // TODO: Implement escalation logic
    return {
      action: AutomationAction.ESCALATE_WORKFLOW,
      success: true,
      executionTime: 0,
    };
  }

  private async executeSendNotification(changeRequest: ChangeRequest, context?: AutomationContext): Promise<AutomationActionResult> {
    // TODO: Implement notification sending
    return {
      action: AutomationAction.SEND_NOTIFICATION,
      success: true,
      executionTime: 0,
    };
  }

  private async executeUpdateStatus(changeRequest: ChangeRequest, context?: AutomationContext): Promise<AutomationActionResult> {
    // TODO: Implement status update
    return {
      action: AutomationAction.UPDATE_STATUS,
      success: true,
      executionTime: 0,
    };
  }

  private async executeAssignUser(changeRequest: ChangeRequest, context?: AutomationContext): Promise<AutomationActionResult> {
    // TODO: Implement user assignment
    return {
      action: AutomationAction.ASSIGN_USER,
      success: true,
      executionTime: 0,
    };
  }

  // Helper methods for finding candidates and processing
  private async findAutoApprovalCandidates(): Promise<ChangeRequest[]> {
    return this.changeRequestRepository
      .createQueryBuilder('cr')
      .where('cr.status = :status', { status: ChangeRequestStatus.PENDING_APPROVAL })
      .andWhere('cr.type IN (:...types)', { 
        types: this.automationConfig.rules.autoApproval.conditions.allowedTypes 
      })
      .getMany();
  }

  private async findOverdueWorkflows(): Promise<ApprovalWorkflow[]> {
    const overdueThreshold = new Date();
    overdueThreshold.setHours(overdueThreshold.getHours() - this.automationConfig.rules.autoEscalation.thresholds.overdueDays * 24);

    return this.workflowRepository
      .createQueryBuilder('wf')
      .where('wf.status = :status', { status: WorkflowStatus.ACTIVE })
      .andWhere('wf.lastActivityAt < :threshold', { threshold: overdueThreshold })
      .getMany();
  }

  private async findAssessmentCandidates(): Promise<ChangeRequest[]> {
    return this.changeRequestRepository
      .createQueryBuilder('cr')
      .leftJoin('impact_assessments', 'ia', 'ia.changeRequestId = cr.id')
      .where('cr.status IN (:...statuses)', { 
        statuses: [ChangeRequestStatus.SUBMITTED, ChangeRequestStatus.UNDER_REVIEW] 
      })
      .andWhere('ia.id IS NULL') // No existing assessment
      .andWhere('cr.businessJustification IS NOT NULL')
      .andWhere('cr.technicalDetails IS NOT NULL')
      .getMany();
  }

  private async isEligibleForAutoApproval(
    changeRequest: ChangeRequest,
    assessment?: ImpactAssessment,
  ): Promise<boolean> {
    if (!assessment) return false;

    const config = this.automationConfig.rules.autoApproval.conditions;

    // Check cost threshold
    if (assessment.estimatedAdditionalCost > config.maxCost) return false;

    // Check hours threshold
    if (assessment.estimatedEffortHours > config.maxHours) return false;

    // Check allowed types
    if (!config.allowedTypes.includes(changeRequest.type)) return false;

    // Check confidence level
    const confidenceScore = this.getConfidenceScore(assessment.confidenceLevel);
    if (confidenceScore < config.requiredConfidence) return false;

    return true;
  }

  private getConfidenceScore(confidence: ConfidenceLevel): number {
    const scores = {
      [ConfidenceLevel.VERY_LOW]: 0.1,
      [ConfidenceLevel.LOW]: 0.3,
      [ConfidenceLevel.MEDIUM]: 0.5,
      [ConfidenceLevel.HIGH]: 0.8,
      [ConfidenceLevel.VERY_HIGH]: 0.95,
    };
    return scores[confidence] || 0.5;
  }

  private async executeAutoApproval(
    changeRequest: ChangeRequest,
    assessment: ImpactAssessment,
  ): Promise<AutomationResult> {
    // Implementation would go here
    return {
      success: true,
      trigger: AutomationTrigger.IMPACT_ASSESSMENT_COMPLETED,
      changeRequestId: changeRequest.id,
      actionsExecuted: 1,
      actions: [],
      executionTime: 0,
    };
  }

  private async executeEscalation(workflow: ApprovalWorkflow): Promise<AutomationResult> {
    // Implementation would go here
    return {
      success: true,
      trigger: AutomationTrigger.WORKFLOW_OVERDUE,
      changeRequestId: workflow.changeRequestId,
      actionsExecuted: 1,
      actions: [],
      executionTime: 0,
    };
  }

  private async executeAutoAssessment(changeRequest: ChangeRequest): Promise<AutomationResult> {
    // Implementation would go here
    return {
      success: true,
      trigger: AutomationTrigger.CHANGE_REQUEST_CREATED,
      changeRequestId: changeRequest.id,
      actionsExecuted: 1,
      actions: [],
      executionTime: 0,
    };
  }

  private async processWorkflowTimeouts(): Promise<void> {
    // Implementation would go here
  }

  private async cleanupExpiredWorkflows(): Promise<void> {
    // Implementation would go here
  }

  private async cleanupAutomationLogs(): Promise<void> {
    // Implementation would go here
  }
}

// Type definitions
interface AutomationRule {
  id: string;
  trigger: AutomationTrigger;
  conditions: AutomationRuleCondition[];
  action: AutomationAction;
  priority: number;
  enabled: boolean;
}

interface AutomationRuleCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'not_in' | 'exists' | 'contains';
  value: any;
}

interface AutomationContext {
  assessment?: ImpactAssessment;
  workflow?: ApprovalWorkflow;
  user?: User;
  metadata?: Record<string, any>;
}

interface AutomationResult {
  success: boolean;
  trigger: AutomationTrigger;
  changeRequestId: string;
  actionsExecuted: number;
  actions: AutomationActionResult[];
  executionTime: number;
  error?: string;
}

interface AutomationActionResult {
  action: AutomationAction;
  success: boolean;
  executionTime: number;
  data?: Record<string, any>;
  error?: string;
}
