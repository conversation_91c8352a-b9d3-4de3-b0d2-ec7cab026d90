import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import {
  Prototype,
  PrototypeVersion,
  VisualFeedback,
  DesignApprovalWorkflow,
  ApprovalStep,
  ApprovalAction,
  CommentDiscussion,
  CommentMention,
} from './entities';
import { PrototypesController } from './controllers/prototypes.controller';
import { FigmaIntegrationController } from './controllers/figma-integration.controller';
import { VisualFeedbackController } from './controllers/visual-feedback.controller';
import { DesignApprovalWorkflowController } from './controllers/design-approval-workflow.controller';
import { CommentDiscussionController } from './controllers/comment-discussion.controller';
import { PrototypesService } from './services/prototypes.service';
import { FigmaIntegrationService } from './services/figma-integration.service';
import { VisualFeedbackService } from './services/visual-feedback.service';
import { DesignApprovalWorkflowService } from './services/design-approval-workflow.service';
import { ApprovalActionService } from './services/approval-action.service';
import { CommentDiscussionService } from './services/comment-discussion.service';

/**
 * PrototypesModule
 *
 * Sprint 8: Prototype Integration & Feedback Tools Module
 *
 * This module provides:
 * - Figma prototype integration and version management
 * - Visual feedback and annotation system
 * - Design approval workflows with multi-step processes
 * - Threaded discussions and comment system with mentions
 *
 * Key Features:
 * - Coordinate-based visual feedback on prototypes
 * - Version control with branching support
 * - Configurable approval workflows (sequential/parallel)
 * - Real-time collaboration through comments and mentions
 * - Comprehensive audit trail and status tracking
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Core prototype entities
      Prototype,
      PrototypeVersion,

      // Visual feedback system
      VisualFeedback,

      // Approval workflow system
      DesignApprovalWorkflow,
      ApprovalStep,
      ApprovalAction,

      // Discussion and comment system
      CommentDiscussion,
      CommentMention,
    ]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
  ],
  controllers: [
    PrototypesController,
    FigmaIntegrationController,
    VisualFeedbackController,
    DesignApprovalWorkflowController,
    CommentDiscussionController,
  ],
  providers: [
    PrototypesService,
    FigmaIntegrationService,
    VisualFeedbackService,
    DesignApprovalWorkflowService,
    ApprovalActionService,
    CommentDiscussionService,
  ],
  exports: [
    TypeOrmModule,
    PrototypesService,
    FigmaIntegrationService,
    VisualFeedbackService,
    DesignApprovalWorkflowService,
    ApprovalActionService,
    CommentDiscussionService,
  ],
})
export class PrototypesModule {}
