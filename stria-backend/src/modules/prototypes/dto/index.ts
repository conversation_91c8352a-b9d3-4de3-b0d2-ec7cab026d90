// Sprint 8: Prototype Integration & Feedback Tools DTOs

// Prototype DTOs
export * from './create-prototype.dto';
export * from './update-prototype.dto';
export * from './prototype-response.dto';
export * from './prototype-query.dto';

// Visual Feedback DTOs
export * from './create-visual-feedback.dto';
export * from './update-visual-feedback.dto';
export * from './visual-feedback-response.dto';
export * from './visual-feedback-query.dto';

// Design Approval Workflow DTOs
export * from './create-design-approval-workflow.dto';
export * from './update-design-approval-workflow.dto';
export * from './design-approval-workflow-response.dto';
export * from './design-approval-workflow-query.dto';
export * from './create-approval-action.dto';

// Comment Discussion DTOs
export * from './create-comment-discussion.dto';
export * from './update-comment-discussion.dto';
export * from './add-comment.dto';
export * from './comment-reaction.dto';
export * from './comment-discussion-response.dto';
export * from './comment-discussion-query.dto';

// Figma Integration DTOs
export * from './figma-sync.dto';

// Common DTOs
export * from './paginated-response.dto';
