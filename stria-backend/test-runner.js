#!/usr/bin/env node

/**
 * Simple test runner to verify our test setup
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Stria Change Management System - Test Runner');
console.log('================================================\n');

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('❌ node_modules not found. Installing dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed successfully\n');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies found\n');
}

// Check if test files exist
const testFiles = [
  'src/modules/change-management/services/__tests__/change-request.service.spec.ts',
  'src/modules/change-management/services/__tests__/impact-assessment.service.spec.ts',
  'src/modules/change-management/__tests__/change-management.integration.spec.ts'
];

console.log('📁 Checking test files:');
testFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - NOT FOUND`);
  }
});
console.log();

// Check package.json scripts
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  console.log('📋 Available test scripts:');
  if (packageJson.scripts) {
    Object.keys(packageJson.scripts).filter(key => key.includes('test')).forEach(script => {
      console.log(`  • npm run ${script}: ${packageJson.scripts[script]}`);
    });
  }
  console.log();
}

// Try to run tests
console.log('🚀 Running tests...\n');

try {
  console.log('1️⃣ Running unit tests...');
  execSync('npm run test -- --testPathPattern="change-management.*spec.ts" --verbose', { 
    stdio: 'inherit',
    timeout: 60000 
  });
  console.log('✅ Unit tests completed\n');
} catch (error) {
  console.log('❌ Unit tests failed or not configured properly');
  console.log('Error:', error.message);
  console.log();
}

try {
  console.log('2️⃣ Running integration tests...');
  execSync('npm run test:e2e -- --testPathPattern="change-management.*integration.spec.ts" --verbose', { 
    stdio: 'inherit',
    timeout: 120000 
  });
  console.log('✅ Integration tests completed\n');
} catch (error) {
  console.log('❌ Integration tests failed or not configured properly');
  console.log('Error:', error.message);
  console.log();
}

try {
  console.log('3️⃣ Generating test coverage...');
  execSync('npm run test:cov -- --testPathPattern="change-management"', { 
    stdio: 'inherit',
    timeout: 120000 
  });
  console.log('✅ Coverage report generated\n');
} catch (error) {
  console.log('❌ Coverage generation failed or not configured properly');
  console.log('Error:', error.message);
  console.log();
}

console.log('🏁 Test execution completed!');
console.log('Check the output above for detailed results.');
