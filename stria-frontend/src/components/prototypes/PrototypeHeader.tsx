'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft,
  Share2,
  ExternalLink,
  Clock,
  User,
  Tag,
  GitBranch
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Prototype Header Component
 * STRIA-141: Figma原型嵌入界面
 * 
 * Header component for prototype view page
 * Shows prototype info, navigation, and actions
 */

interface PrototypeHeaderProps {
  prototype: {
    id: string;
    name: string;
    description?: string;
    type: string;
    status: string;
    currentVersion: string;
    versionCount: number;
    createdAt: string;
    updatedAt: string;
    project: {
      id: string;
      name: string;
    };
    creator: {
      id: string;
      name: string;
      email: string;
    };
    metadata: {
      tags?: string[];
      figmaNodeIds?: string[];
    };
  };
  onBack: () => void;
  onShare: () => void;
  className?: string;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'in_review':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'published':
      return 'bg-blue-100 text-blue-800';
    case 'archived':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getTypeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case 'wireframe':
      return 'bg-purple-100 text-purple-800';
    case 'mockup':
      return 'bg-indigo-100 text-indigo-800';
    case 'high_fidelity':
      return 'bg-emerald-100 text-emerald-800';
    case 'interactive':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export function PrototypeHeader({
  prototype,
  onBack,
  onShare,
  className
}: PrototypeHeaderProps) {
  return (
    <header className={cn(
      "bg-white border-b border-gray-200 px-6 py-4",
      className
    )}>
      <div className="flex items-center justify-between">
        {/* Left Section - Navigation & Info */}
        <div className="flex items-center gap-4">
          {/* Back Button */}
          <Button
            onClick={onBack}
            variant="ghost"
            size="sm"
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Project
          </Button>

          {/* Divider */}
          <div className="h-6 w-px bg-gray-300" />

          {/* Prototype Info */}
          <div className="flex items-center gap-3">
            {/* Project Name */}
            <span className="text-sm text-gray-500">
              {prototype.project.name}
            </span>
            <span className="text-gray-300">/</span>
            
            {/* Prototype Name */}
            <h1 className="text-xl font-semibold text-gray-900">
              {prototype.name}
            </h1>

            {/* Status Badge */}
            <Badge className={cn("text-xs", getStatusColor(prototype.status))}>
              {prototype.status.replace('_', ' ')}
            </Badge>

            {/* Type Badge */}
            <Badge className={cn("text-xs", getTypeColor(prototype.type))}>
              {prototype.type.replace('_', ' ')}
            </Badge>
          </div>
        </div>

        {/* Right Section - Actions & Meta */}
        <div className="flex items-center gap-4">
          {/* Version Info */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <GitBranch className="h-4 w-4" />
            <span>v{prototype.currentVersion}</span>
            <span className="text-gray-400">
              ({prototype.versionCount} versions)
            </span>
          </div>

          {/* Creator Info */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <User className="h-4 w-4" />
            <span>{prototype.creator.name}</span>
          </div>

          {/* Last Updated */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span>Updated {formatDate(prototype.updatedAt)}</span>
          </div>

          {/* Divider */}
          <div className="h-6 w-px bg-gray-300" />

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Button
              onClick={onShare}
              variant="outline"
              size="sm"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>

            <Button
              onClick={() => window.open(`/prototypes/${prototype.id}/edit`, '_blank')}
              variant="outline"
              size="sm"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </div>
        </div>
      </div>

      {/* Second Row - Description & Tags */}
      {(prototype.description || prototype.metadata.tags?.length) && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-start justify-between">
            {/* Description */}
            {prototype.description && (
              <p className="text-sm text-gray-600 max-w-2xl">
                {prototype.description}
              </p>
            )}

            {/* Tags */}
            {prototype.metadata.tags?.length && (
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-gray-400" />
                <div className="flex gap-1">
                  {prototype.metadata.tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
}
