'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft,
  Maximize2,
  Minimize2,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Share2,
  MessageSquare,
  Eye,
  Settings,
  Download,
  ExternalLink,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { FigmaPrototypeEmbed } from '@/components/prototypes/FigmaPrototypeEmbed';
import { PrototypeHeader } from '@/components/prototypes/PrototypeHeader';
import { PrototypeControls } from '@/components/prototypes/PrototypeControls';
import { PrototypeInfo } from '@/components/prototypes/PrototypeInfo';
import { cn } from '@/lib/utils';

/**
 * Prototype View Page
 * STRIA-141: Figma原型嵌入界面
 * 
 * Main page for viewing Figma prototypes with embedded iframe
 * Supports full-screen mode, zoom controls, and prototype interaction
 */

interface PrototypeData {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  figmaFileId?: string;
  figmaFileUrl?: string;
  figmaIntegrationStatus: string;
  currentVersion: string;
  versionCount: number;
  createdAt: string;
  updatedAt: string;
  project: {
    id: string;
    name: string;
  };
  creator: {
    id: string;
    name: string;
    email: string;
  };
  metadata: {
    figmaNodeIds?: string[];
    designSpecs?: {
      width?: number;
      height?: number;
      scale?: number;
    };
    collaborators?: string[];
    tags?: string[];
  };
}

interface ViewState {
  isFullscreen: boolean;
  zoom: number;
  showInfo: boolean;
  showComments: boolean;
  isLoading: boolean;
  error: string | null;
}

export default function PrototypeViewPage() {
  const params = useParams();
  const router = useRouter();
  const prototypeId = params.id as string;

  const [prototype, setPrototype] = useState<PrototypeData | null>(null);
  const [viewState, setViewState] = useState<ViewState>({
    isFullscreen: false,
    zoom: 100,
    showInfo: true,
    showComments: false,
    isLoading: true,
    error: null,
  });

  // Load prototype data
  useEffect(() => {
    loadPrototype();
  }, [prototypeId]);

  const loadPrototype = async () => {
    try {
      setViewState(prev => ({ ...prev, isLoading: true, error: null }));
      
      // TODO: Replace with actual API call
      const response = await fetch(`/api/prototypes/${prototypeId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load prototype');
      }
      
      const data = await response.json();
      setPrototype(data);
    } catch (error) {
      console.error('Error loading prototype:', error);
      setViewState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to load prototype' 
      }));
    } finally {
      setViewState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleFullscreenToggle = () => {
    setViewState(prev => ({ ...prev, isFullscreen: !prev.isFullscreen }));
  };

  const handleZoomChange = (newZoom: number) => {
    setViewState(prev => ({ ...prev, zoom: Math.max(25, Math.min(200, newZoom)) }));
  };

  const handleZoomIn = () => handleZoomChange(viewState.zoom + 25);
  const handleZoomOut = () => handleZoomChange(viewState.zoom - 25);
  const handleZoomReset = () => handleZoomChange(100);

  const handleInfoToggle = () => {
    setViewState(prev => ({ ...prev, showInfo: !prev.showInfo }));
  };

  const handleCommentsToggle = () => {
    setViewState(prev => ({ ...prev, showComments: !prev.showComments }));
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: prototype?.name || 'Prototype',
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to clipboard
      await navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleBackToProject = () => {
    if (prototype?.project?.id) {
      router.push(`/projects/${prototype.project.id}`);
    } else {
      router.push('/projects');
    }
  };

  // Loading state
  if (viewState.isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading prototype...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (viewState.error || !prototype) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Error Loading Prototype
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              {viewState.error || 'Prototype not found'}
            </p>
            <div className="flex gap-2">
              <Button onClick={loadPrototype} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button onClick={handleBackToProject}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Project
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      viewState.isFullscreen && "fixed inset-0 z-50 bg-white"
    )}>
      {/* Header */}
      {!viewState.isFullscreen && (
        <PrototypeHeader
          prototype={prototype}
          onBack={handleBackToProject}
          onShare={handleShare}
        />
      )}

      {/* Main Content */}
      <div className={cn(
        "flex",
        viewState.isFullscreen ? "h-screen" : "h-[calc(100vh-4rem)]"
      )}>
        {/* Sidebar - Info Panel */}
        {viewState.showInfo && !viewState.isFullscreen && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <PrototypeInfo prototype={prototype} />
          </div>
        )}

        {/* Main Prototype View */}
        <div className="flex-1 flex flex-col">
          {/* Controls Bar */}
          <PrototypeControls
            zoom={viewState.zoom}
            isFullscreen={viewState.isFullscreen}
            showInfo={viewState.showInfo}
            showComments={viewState.showComments}
            onZoomIn={handleZoomIn}
            onZoomOut={handleZoomOut}
            onZoomReset={handleZoomReset}
            onFullscreenToggle={handleFullscreenToggle}
            onInfoToggle={handleInfoToggle}
            onCommentsToggle={handleCommentsToggle}
          />

          {/* Figma Embed */}
          <div className="flex-1 relative">
            <FigmaPrototypeEmbed
              figmaFileUrl={prototype.figmaFileUrl}
              figmaFileId={prototype.figmaFileId}
              zoom={viewState.zoom}
              isFullscreen={viewState.isFullscreen}
              designSpecs={prototype.metadata.designSpecs}
            />
          </div>
        </div>

        {/* Comments Sidebar */}
        {viewState.showComments && !viewState.isFullscreen && (
          <div className="w-80 bg-white border-l border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Comments & Feedback</h3>
              <p className="text-gray-500 text-sm">
                Comments and feedback will be displayed here.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Fullscreen Exit Button */}
      {viewState.isFullscreen && (
        <Button
          onClick={handleFullscreenToggle}
          className="fixed top-4 right-4 z-10"
          variant="outline"
          size="sm"
        >
          <Minimize2 className="h-4 w-4 mr-2" />
          Exit Fullscreen
        </Button>
      )}
    </div>
  );
}
